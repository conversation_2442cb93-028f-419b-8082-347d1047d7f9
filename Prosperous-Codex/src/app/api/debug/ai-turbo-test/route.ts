import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/middleware';
import { AITurboService } from '@/lib/services/ai-turbo-service';

/**
 * DEBUG ENDPOINT: Test AI Turbo response format
 * This endpoint will show us the raw AI response for debugging
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const body = await request.json();
    const inputText = body.inputText || 'Create a simple website for a bakery';

    console.log('=== DEBUG AI TURBO TEST ===');
    console.log('Input:', inputText);

    // Initialize AI turbo service
    const aiTurboService = new AITurboService();

    if (!aiTurboService.isAvailable()) {
      return NextResponse.json({
        error: 'AI service not available',
        configured: aiTurboService.isServiceConfigured(),
        provider: aiTurboService.getProvider()
      });
    }

    try {
      // Try to get the raw response without parsing
      const result = await aiTurboService.enhanceAndParse(inputText);
      
      return NextResponse.json({
        success: true,
        message: 'AI Turbo worked successfully!',
        result: {
          title: result.title,
          description: result.description?.substring(0, 200) + '...',
          hasReasoningContent: !!result.reasoning_content,
          tasksCount: result.tasks?.length || 0
        }
      });

    } catch (error) {
      console.error('AI Turbo Test Error:', error);
      
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorType: error?.constructor?.name,
        details: 'Check server console for full AI response logs'
      });
    }

  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json(
      { error: 'Debug endpoint failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to test service availability
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const aiTurboService = new AITurboService();
    
    return NextResponse.json({
      available: aiTurboService.isAvailable(),
      configured: aiTurboService.isServiceConfigured(),
      provider: aiTurboService.getProvider(),
      message: 'Debug endpoint is working'
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to check service status' },
      { status: 500 }
    );
  }
}
