import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/middleware';
import { AITurboService } from '@/lib/services/ai-turbo-service';
import { FieldMapper } from '@/lib/task-master/field-mapping';

/**
 * POST /api/task-master/ai-turbo
 * 
 * Combines AI enhancement and parsing into a single operation for improved efficiency.
 * Requires authentication and follows camelCase ↔ snake_case field mapping.
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;

    // Parse and validate request body
    const body = await request.json();
    
    // Apply field mapping from camelCase to snake_case
    const mappedBody = FieldMapper.apiToDb(body);
    
    // Validate required fields
    if (!mappedBody.input_text || typeof mappedBody.input_text !== 'string') {
      return NextResponse.json(
        { error: 'Missing or invalid input_text field' },
        { status: 400 }
      );
    }

    // Validate input length (prevent abuse)
    const maxInputLength = parseInt(process.env.AI_MAX_INPUT_LENGTH || '65536');
    if (mappedBody.input_text.length > maxInputLength) {
      return NextResponse.json(
        { error: `Input text too long (maximum ${maxInputLength.toLocaleString()} characters)` },
        { status: 400 }
      );
    }

    if (mappedBody.input_text.trim().length === 0) {
      return NextResponse.json(
        { error: 'Input text cannot be empty' },
        { status: 400 }
      );
    }

    // Initialize AI turbo service
    const aiTurboService = new AITurboService();

    // Check if AI service is available
    if (!aiTurboService.isAvailable()) {
      return NextResponse.json(
        { error: 'AI turbo service is not available. Please check configuration.' },
        { status: 503 }
      );
    }

    // Enhance and parse the text in a single operation
    const turboResult = await aiTurboService.enhanceAndParse(
      mappedBody.input_text,
      {
        context: mappedBody.context || 'task_description',
        tone: mappedBody.tone || 'professional',
        max_tokens: mappedBody.max_tokens || parseInt(process.env.AI_MAX_TOKENS || '65536')
      }
    );

    // Prepare response with field mapping back to camelCase
    const response = FieldMapper.dbToApi({
      enhanced_text: turboResult.enhanced_text,
      original_text: turboResult.original_text,
      enhancement_applied: turboResult.enhancement_applied,
      model_used: turboResult.model_used,
      tokens_used: turboResult.tokens_used,
      reasoning_content: turboResult.reasoning_content,
      title: turboResult.title,
      event_log: turboResult.event_log,
      description: turboResult.description,
      tasks: turboResult.tasks
    });

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('AI Turbo API Error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error' },
          { status: 503 }
        );
      }
      
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }

      if (error.message.includes('content filter')) {
        return NextResponse.json(
          { error: 'Content cannot be processed due to safety guidelines' },
          { status: 400 }
        );
      }

      if (error.message.includes('Could not extract')) {
        return NextResponse.json(
          { error: 'Failed to parse AI response. Please try again.' },
          { status: 500 }
        );
      }

      if (error.message.includes('Invalid JSON')) {
        return NextResponse.json(
          { error: 'AI response format error. Please try again.' },
          { status: 500 }
        );
      }

      if (error.message.includes('Missing required fields')) {
        return NextResponse.json(
          { error: 'Incomplete AI response. Please try again.' },
          { status: 500 }
        );
      }
    }

    // Generic error response
    return NextResponse.json(
      { error: 'Failed to process turbo request. Please try again.' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/task-master/ai-turbo
 * 
 * Returns the status of the AI turbo service
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const aiTurboService = new AITurboService();
    
    return NextResponse.json({
      available: aiTurboService.isAvailable(),
      configured: aiTurboService.isServiceConfigured(),
      provider: aiTurboService.getProvider()
    });

  } catch (error) {
    console.error('AI Turbo Status Error:', error);
    return NextResponse.json(
      { error: 'Failed to check service status' },
      { status: 500 }
    );
  }
}
