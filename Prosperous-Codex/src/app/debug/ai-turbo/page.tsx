"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';

export default function AITurboDebugPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [inputText, setInputText] = useState('Create a simple website for a bakery');

  const testAITurbo = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/debug/ai-turbo-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ inputText }),
      });

      const data = await response.json();
      setResult(data);

    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const checkServiceStatus = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/debug/ai-turbo-test');
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">AI Turbo Debug Page</h1>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">Test Input:</label>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="w-full p-3 border rounded-md h-24"
            placeholder="Enter test input..."
          />
        </div>

        <div className="flex gap-4">
          <Button 
            onClick={checkServiceStatus}
            disabled={loading}
            variant="outline"
          >
            Check Service Status
          </Button>
          
          <Button 
            onClick={testAITurbo}
            disabled={loading}
            variant="default"
          >
            {loading ? 'Testing...' : 'Test AI Turbo'}
          </Button>
        </div>

        {result && (
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-3">Result:</h2>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
          <h3 className="font-semibold mb-2">Instructions:</h3>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>First click "Check Service Status" to verify the AI service is configured</li>
            <li>Then click "Test AI Turbo" to test the actual functionality</li>
            <li>Check your server console/terminal for detailed logs</li>
            <li>If there are errors, the detailed logs will show the raw AI response</li>
          </ol>
        </div>

        <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
          <h3 className="font-semibold mb-2">What to Look For:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li><strong>Server Console:</strong> Look for "=== RAW AI TURBO RESPONSE ===" in your terminal</li>
            <li><strong>Parsing Logs:</strong> Look for "=== PARSING TURBO RESPONSE ===" messages</li>
            <li><strong>Error Details:</strong> Look for "=== AI TURBO API ERROR ===" for specific issues</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
