"use client";

// Card components not used in current implementation
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowUp, AppWindow, Calculator, TrendingUp } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function MetricsGrid() {
  const router = useRouter();

  const handleTaskMasterClick = () => {
    router.push('/task-master');
  };

  const handleEstimateClick = () => {
    router.push('/calculator');
  };

  return (
    <div className="flex w-full flex-wrap items-start gap-4">
      {/* Active Projects Card */}
      <div className="flex grow shrink-0 basis-0 flex-col items-start gap-4 rounded-md border border-border bg-card/50 backdrop-blur-sm px-4 py-4 shadow-sm">
        <span className="line-clamp-1 w-full text-xs font-semibold text-muted-foreground tracking-wide">
          Active Projects
        </span>
        <div className="flex w-full flex-col items-start gap-2">
          <span className="text-xl font-semibold text-foreground">
            12
          </span>
          <Badge variant="success" icon={<ArrowUp className="h-3 w-3" />}>
            4 new
          </Badge>
        </div>
        <Button
          className="h-8 w-full flex-none"
          variant="brand-primary"
          onClick={handleTaskMasterClick}
        >
          <AppWindow className="h-4 w-4" />
          Task Master
        </Button>
      </div>

      {/* Tasks Completed Card */}
      <div className="flex grow shrink-0 basis-0 flex-col items-start gap-4 rounded-md border border-border bg-card/50 backdrop-blur-sm px-4 py-4 shadow-sm">
        <span className="line-clamp-1 w-full text-xs font-semibold text-muted-foreground tracking-wide">
          Tasks Completed
        </span>
        <div className="flex w-full flex-col items-start gap-2">
          <span className="text-xl font-semibold text-foreground">
            64
          </span>
          <Badge variant="success" icon={<ArrowUp className="h-3 w-3" />}>
            12% increase
          </Badge>
        </div>
      </div>

      {/* Estimation Tools Card */}
      <div className="flex grow shrink-0 basis-0 flex-col items-start gap-4 rounded-md border border-border bg-card/50 backdrop-blur-sm px-4 py-4 shadow-sm">
        <span className="line-clamp-1 w-full text-xs font-semibold text-muted-foreground tracking-wide">
          Estimation Tools
        </span>
        <div className="flex w-full flex-col items-start gap-2">
          <span className="text-xl font-semibold text-foreground">
            8
          </span>
          <Badge variant="neutral" icon={<TrendingUp className="h-3 w-3" />}>
            Last updated MM/YYYY
          </Badge>
        </div>
        <Button
          className="h-8 w-full flex-none"
          variant="brand-primary"
          onClick={handleEstimateClick}
        >
          <Calculator className="h-4 w-4" />
          Estimate
        </Button>
      </div>
    </div>
  );
}
