"use client";

import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';
import { ChevronRight, CheckCircle, MessageSquare, UserPlus } from 'lucide-react';

interface IconWithBackgroundProps {
  variant?: 'success' | 'neutral' | 'brand';
  icon?: React.ReactNode;
}

function IconWithBackground({ variant = 'brand', icon }: IconWithBackgroundProps) {
  const variantClasses = {
    success: 'bg-green-100 text-green-600 dark:bg-emerald-900/20 dark:text-emerald-400',
          neutral: "bg-neutral-200 dark:bg-neutral-800",
    brand: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
  };

  return (
    <div className={`flex h-8 w-8 items-center justify-center rounded-full ${variantClasses[variant]}`}>
      {icon || <CheckCircle className="h-4 w-4" />}
    </div>
  );
}

export function RecentActivity() {
  const t = useTranslations('dashboard.activity');
  const activities = [
    {
      id: 1,
      icon: <CheckCircle className="h-4 w-4" />,
      variant: 'success' as const,
      title: 'Website Redesign completed',
      description: 'Project milestone achieved',
      time: '2h ago',
    },
    {
      id: 2,
      icon: <MessageSquare className="h-4 w-4" />,
      variant: 'neutral' as const,
      title: 'New comment on Mobile App',
      description: 'Sarah left a review',
      time: '4h ago',
    },
    {
      id: 3,
      icon: <UserPlus className="h-4 w-4" />,
      variant: 'neutral' as const,
      title: 'New team member added',
      description: 'Mike joined the design team',
      time: '6h ago',
    },
  ];

  return (
    <div className="flex w-full flex-col items-start gap-4">
      <div className="flex w-full items-center justify-between">
        <span className="text-base font-semibold text-foreground">
          {t('title')}
        </span>
        <Button variant="neutral-tertiary" size="sm">
          View all
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex w-full flex-col items-start rounded-md border border-border bg-card/50 backdrop-blur-sm shadow-sm">
        {activities.map((activity, index) => (
          <div key={activity.id} className="w-full">
            <div className="flex w-full items-center gap-4 px-4 py-4">
              <IconWithBackground variant={activity.variant} icon={activity.icon} />
              <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1">
                <span className="w-full text-sm font-medium text-foreground">
                  {activity.title}
                </span>
                <span className="w-full text-xs text-muted-foreground">
                  {activity.description}
                </span>
              </div>
              <span className="text-sm text-muted-foreground">
                {activity.time}
              </span>
            </div>
            {index < activities.length - 1 && (
              <div className="h-px w-full bg-border" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
