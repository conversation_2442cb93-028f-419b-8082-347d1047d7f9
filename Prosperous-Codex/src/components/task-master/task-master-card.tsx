"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import { User, Flag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar } from '@/components/ui/avatar';
import { TaskDescriptionText, CommentText } from '@/components/ui/formatted-text';
import { TaskMaster } from '@/lib/types/task-master';
import { useDrag } from 'react-dnd';

// Predefined accent colors for consistent visual appeal
const ACCENT_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Emerald
  '#F59E0B', // Amber
  '#EF4444', // Red
  '#8B5CF6', // Violet
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#F97316', // Orange
  '#EC4899', // Pink
  '#6366F1', // Indigo
  '#14B8A6', // Teal
  '#A855F7', // Purple
];

// Generate consistent accent color based on task ID
const getAccentColor = (taskId: string | number): string => {
  const id = String(taskId);
  let hash = 0;
  for (let i = 0; i < id.length; i++) {
    const char = id.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  const index = Math.abs(hash) % ACCENT_COLORS.length;
  return ACCENT_COLORS[index];
};

interface TaskMasterCardProps {
  task: TaskMaster;
  variant: 'neutral' | 'warning' | 'success' | 'brand';
  onTaskClick?: (task: TaskMaster) => void;
  columnId: string;
  onDragStart?: (item: { id: string; title: string; createdBy: number }) => void;
  onDragEnd?: () => void;
  isAnimating?: boolean;
  // Turbo mode loading overlay props
  isTurboProcessing?: boolean;
  turboProcessingCardId?: string | null;
  turboProgressText?: string;
}

export function TaskMasterCard({
  task,
  variant,
  onTaskClick,
  columnId,
  onDragStart,
  onDragEnd,
  isAnimating = false,
  isTurboProcessing = false,
  turboProcessingCardId = null,
  turboProgressText = ''
}: TaskMasterCardProps) {
  // State for hover-based accordion
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  // Determine if this card should show the turbo loading overlay
  const shouldShowTurboOverlay = isTurboProcessing && turboProcessingCardId === task.id.toString();

  // Server-based progress calculation (cards should show server state, not drawer state)
  const getProgressValue = useCallback(() => {
    // Prioritize backend value for cards (server-based progress)
    // This ensures cards show committed progress, not temporary drawer changes
    if (task.progress !== undefined && task.progress !== null && !isNaN(task.progress)) {
      return Math.max(0, Math.min(100, task.progress)); // Clamp between 0-100
    }

    // Fallback to frontend calculation only if no server progress available
    if (task.tasks && task.tasks.length > 0) {
      const completedTasks = task.tasks.filter(t => t.status === 'completed').length;
      const progress = Math.round((completedTasks / task.tasks.length) * 100);
      return Math.max(0, Math.min(100, progress)); // Clamp between 0-100
    }

    return 0;
  }, [task.progress, task.tasks]);

  // Drag functionality - using the standard React DnD pattern
  const [{ isDragging }, drag, dragPreview] = useDrag(() => ({
    type: 'TASK',
    item: () => {
      // Call onDragStart when drag begins
      onDragStart?.({ id: task.id, title: task.title, createdBy: task.createdBy });

      return {
        id: task.id,
        fromColumnId: columnId,
        createdBy: task.createdBy,
        title: task.title
      };
    },
    end: () => {
      onDragEnd?.();
    },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }), [task.id, columnId, task.createdBy, task.title, onDragStart, onDragEnd]);

  // Connect drag preview to the card element using the standard pattern
  const connectDragPreview = (node: HTMLDivElement | null) => {
    if (node) {
      dragPreview(node);
    }
  };
  const getBorderColor = (variant: string, isPrivate: boolean = false) => {
    if (isPrivate) {
      return 'border-red-300 dark:border-red-600';
    }
    switch (variant) {
      case 'warning':
        return 'border-yellow-300 dark:border-yellow-600';
      case 'success':
        return 'border-emerald-300 dark:border-emerald-600';
      default:
        return 'border-[#5E6AD2] dark:border-[#6E56CF]';
    }
  };

  const getBackgroundColor = (isPrivate: boolean = false) => {
    if (isPrivate) {
      return 'bg-red-50/50 dark:bg-red-900/10';
    }
    return 'bg-white dark:bg-[#1A1A1A]';
  };

  const getRingColor = (isPrivate: boolean = false) => {
    if (isPrivate) {
      return 'ring-red-300/30 dark:ring-red-600/30';
    }
    return 'ring-[#5E6AD2]/20 dark:ring-[#6E56CF]/20';
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-800 dark:text-neutral-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-800 dark:text-neutral-300';
    }
  };

  const getProgressColor = (variant: string) => {
    switch (variant) {
      case 'warning':
        return 'text-yellow-700 dark:text-yellow-400';
      case 'success':
        return 'text-emerald-700 dark:text-emerald-400';
      default:
        return 'text-[#5E6AD2] dark:text-[#6E56CF]';
    }
  };

  const getProgressVariant = (variant: string) => {
    switch (variant) {
      case 'warning':
        return 'warning' as const;
      case 'success':
        return 'success' as const;
      default:
        return 'brand' as const;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  };

  // Hover handlers for accordion functionality
  const handleMouseEnter = () => {
    setIsHovering(true);
    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    // Set timeout for 1.2 seconds
    hoverTimeoutRef.current = setTimeout(() => {
      setIsExpanded(true);
    }, 1000);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
    // Clear timeout if mouse leaves before 1.5 seconds
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    // Immediately collapse
    setIsExpanded(false);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // Get latest comment for preview
  const getLatestComment = () => {
    if (task.projectDetails?.comments && task.projectDetails.comments.length > 0) {
      return task.projectDetails.comments[task.projectDetails.comments.length - 1];
    }
    return null;
  };

  const latestComment = getLatestComment();

  return (
    <div
      className={`w-full relative ${isAnimating ? 'opacity-0 pointer-events-none' : ''}`}
      ref={cardRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ zIndex: isExpanded ? 10 : 1 }}
      data-task-card={task.id}
    >
      {/* Main Card - Always maintains its complete appearance */}
      <div
        ref={(node) => {
          // Connect both drag preview and drag source to the same element
          connectDragPreview(node);
          drag(node);
        }}
        className={`task-card-content relative flex w-full flex-col items-start gap-4 rounded-md border border-solid ${getBorderColor(variant, task.visibility === 'private')} ${getBackgroundColor(task.visibility === 'private')} px-4 py-6 shadow-md hover:shadow-lg transition-all duration-300 ${isDragging ? 'opacity-50 cursor-grabbing' : 'cursor-grab'} ${isHovering ? `ring-2 ${getRingColor(task.visibility === 'private')}` : ''}`}
        onClick={(e) => {
          e.stopPropagation();
          onTaskClick?.(task);
        }}
      >
        {/* Header section with title and description */}
        <div className="w-full relative">
          <div
            className="relative border-l-[5px] rounded-l-sm pl-3"
            style={{ borderColor: getAccentColor(task.id) }}
          >
            {/* Priority Badge - Floating element for text wrapping */}
            <Badge
              variant="secondary"
              className={`float-right ml-2 mb-1 z-20 ${getPriorityBadgeColor(task.priority)}`}
              style={{
                shapeOutside: 'margin-box',
                marginTop: '2px'
              }}
            >
              <Flag className="h-3 w-3 mr-1" />
              {task.priority === 'high' ? 'High' : task.priority === 'medium' ? 'Med' : 'Low'}
            </Badge>

            <div className="w-full">
              <span className="text-sm font-semibold text-foreground text-left break-words leading-tight block">
                {task.title}
              </span>
            </div>
            <div className="w-full mt-1">
              {task.projectDetails?.fullDescription && task.projectDetails.fullDescription.trim() ? (
                <TaskDescriptionText
                  text={task.projectDetails.fullDescription}
                  className="text-left"
                  truncate={true}
                  maxLength={100}
                />
              ) : (
                <span className="text-xs text-muted-foreground italic">
                  No description provided...
                </span>
              )}
            </div>
          </div>
        </div>
        <Progress
          value={getProgressValue()}
          variant={getProgressVariant(variant)}
          className="w-full"
        />
        <div className="flex w-full items-center justify-between">
          <span className="text-xs text-muted-foreground">
            {task.status === 'completed' && task.completedDate
              ? `Completed: ${formatDate(task.completedDate)}`
              : task.dueDate
                ? `Due: ${formatDate(task.dueDate)}`
                : ''
            }
          </span>
          <span className={`text-xs font-semibold ${getProgressColor(variant)}`}>
            {getProgressValue()}% {getProgressValue() === 100 ? 'Complete' : 'Progress'}
          </span>
        </div>
      </div>

      {/* Hover-based Accordion Content - Subtle container with seamless integration */}
      <div
        className={`overflow-hidden transition-all duration-500 ease-in-out mt-2 ${
          isExpanded ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
        }`}
        style={{
          transitionProperty: 'max-height, opacity',
          transitionDuration: '500ms',
          transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        {/* Subtle container with same background as main card */}
        {latestComment && (
          <div className="p-2 w-full overflow-hidden">
            <div className="flex items-start gap-3 w-full">
              <Avatar className="h-6 w-6">
                {latestComment.author.avatar ? (
                  <Image
                    src={latestComment.author.avatar}
                    alt={latestComment.author.name}
                    width={24}
                    height={24}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  </div>
                )}
              </Avatar>
              <div className="flex-1 min-w-0 overflow-hidden">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-xs font-medium text-foreground">
                    {latestComment.author.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatDate(latestComment.createdAt)}
                  </span>
                </div>
                <div className="w-full break-words overflow-wrap-anywhere">
                  <CommentText
                    text={latestComment.content}
                    className="text-xs text-muted-foreground break-words whitespace-pre-wrap word-wrap-break-word"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Turbo Loading Overlay */}
      {shouldShowTurboOverlay && (
        <div className="absolute inset-0 bg-white/80 dark:bg-black/80 backdrop-blur-sm rounded-md flex items-center justify-center z-50">
          <div className="flex flex-col items-center gap-3 p-4">
            {/* Spinner */}
            <div className="relative">
              <div className="w-8 h-8 border-2 border-[#5E6AD2]/20 dark:border-[#6E56CF]/20 rounded-full"></div>
              <div className="absolute inset-0 w-8 h-8 border-2 border-transparent border-t-[#5E6AD2] dark:border-t-[#6E56CF] rounded-full animate-spin"></div>
            </div>

            {/* Progress Text */}
            <div className="text-center">
              <p className="text-sm font-medium text-[#5E6AD2] dark:text-[#6E56CF] mb-1">
                Turbo Mode
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {turboProgressText || 'Processing...'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
