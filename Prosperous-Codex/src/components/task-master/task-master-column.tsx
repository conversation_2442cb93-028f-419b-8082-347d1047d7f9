"use client";

import React from 'react';
import { Inbox, Clock, CheckCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { IconWithBackground } from '@/components/ui/icon-with-background';
import { TaskMasterCard } from './task-master-card';
import { TaskMasterColumn as TaskMasterColumnType, TaskMaster } from '@/lib/types/task-master';
import { useDrop } from 'react-dnd';

interface TaskMasterColumnProps {
  column: TaskMasterColumnType;
  onTaskClick?: (task: TaskMaster) => void;
  onMoveTask?: (taskId: string, fromColumnId: string, toColumnId: string) => void;
  onDragStart?: (item: { id: string; title: string; createdBy: number }) => void;
  onDragEnd?: () => void;
  synchronizedHeight?: number;
  columnRef?: (element: HTMLDivElement | null) => void;
  animatingCardIds?: Set<string>;
  // Turbo mode loading props
  isTurboProcessing?: boolean;
  turboProcessingCardId?: string | null;
  turboProgressText?: string;
}

export function TaskMasterColumn({
  column,
  onTaskClick,
  onMoveTask,
  onDragStart,
  onDragEnd,
  synchronizedHeight,
  columnRef,
  animatingCardIds = new Set(),
  isTurboProcessing = false,
  turboProcessingCardId = null,
  turboProgressText = ''
}: TaskMasterColumnProps) {
  // Drop target for tasks
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'TASK',
    drop: (item: { id: string; fromColumnId: string }) => {
      if (onMoveTask && item.fromColumnId !== column.id) {
        onMoveTask(item.id, item.fromColumnId, column.id);
      }
    },
    canDrop: (item: { id: string; fromColumnId: string }) => {
      // Only allow drops from different columns
      return item.fromColumnId !== column.id;
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
    }),
  }), [column.id, onMoveTask]);

  const getIcon = (iconName: string) => {
    const iconProps = { className: "h-4 w-4" };
    switch (iconName) {
      case 'Inbox':
        return <Inbox {...iconProps} />;
      case 'Clock':
        return <Clock {...iconProps} />;
      case 'CheckCircle':
        return <CheckCircle {...iconProps} />;
      default:
        return <Inbox {...iconProps} />;
    }
  };

  const getBadgeVariant = (variant: string) => {
    switch (variant) {
      case 'warning':
        return 'secondary' as const;
      case 'success':
        return 'secondary' as const;
      case 'brand':
        return 'secondary' as const;
      default:
        return 'secondary' as const;
    }
  };

  const getBadgeClassName = (variant: string) => {
    switch (variant) {
      case 'warning':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'success':
        return 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400';
      case 'brand':
        return 'bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/10 dark:text-[#6E56CF]';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-800 dark:text-neutral-300';
    }
  };

  return (
    <div
      ref={(element) => {
        // Combine the drop ref and column ref
        if (drop) drop(element);
        if (columnRef) columnRef(element);
      }}
      className="relative flex w-80 flex-none flex-col items-start"
      style={{
        minHeight: synchronizedHeight ? `${synchronizedHeight}px` : 'auto'
      }}
    >
      {/* Enhanced Drop Zone Visual Overlay - positioned as background layer */}
      {(isOver && canDrop) || (isOver && !canDrop) ? (
        <div
          className={`absolute inset-0 pointer-events-none z-0 rounded-lg transition-all duration-300 ${
            isOver && canDrop
              ? 'bg-[#5E6AD2]/8 dark:bg-[#6E56CF]/12 ring-2 ring-[#5E6AD2]/40 dark:ring-[#6E56CF]/40'
              : 'bg-red-50 dark:bg-red-900/10 ring-2 ring-red-300/30 dark:ring-red-600/30'
          }`}
        >
          {/* Enhanced placement indicator for valid drops */}
          {isOver && canDrop && (
            <div className="absolute bottom-4 left-4 right-4 flex justify-center">
              <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-[#5E6AD2] dark:bg-[#6E56CF] text-white text-xs font-medium rounded-full shadow-lg animate-pulse">
                <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" />
                Card will be placed here
                <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              </div>
            </div>
          )}
        </div>
      ) : null}

      {/* Column Header */}
      <div className="flex w-full items-center justify-between mb-4 flex-shrink-0 relative z-10">
        <div className="flex items-center gap-2">
          <IconWithBackground
            variant={column.variant}
            size="small"
            icon={getIcon(column.icon)}
          />
          <h3 className="text-lg font-semibold text-foreground">
            {column.title}
          </h3>
        </div>
        <Badge
          variant={getBadgeVariant(column.variant)}
          className={getBadgeClassName(column.variant)}
        >
          {column.tasks.length}
        </Badge>
      </div>

      {/* Task Cards Container - This will expand to fill available space */}
      <div className="flex w-full flex-col gap-4 flex-grow relative z-10" data-cards-container>
        {column.tasks.map((task) => (
          <TaskMasterCard
            key={task.id}
            task={task}
            variant={column.variant}
            onTaskClick={onTaskClick}
            columnId={column.id}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
            isAnimating={animatingCardIds.has(task.id)}
            isTurboProcessing={isTurboProcessing}
            turboProcessingCardId={turboProcessingCardId}
            turboProgressText={turboProgressText}
          />
        ))}

        {/* Invisible drop zone filler - ensures the entire column height is droppable */}
        {synchronizedHeight && (
          <div className="flex-grow min-h-[20px]" />
        )}
      </div>
    </div>
  );
}
