import OpenAI from 'openai';

/**
 * AI Turbo Service
 * 
 * Combines AI enhancement and parsing into a single operation for improved efficiency.
 * Uses the same configuration patterns as existing AI services with enhanced reasoning support.
 */

export interface TurboOptions {
  max_tokens?: number;
  model?: string;
  context?: string;
  tone?: string;
}

export interface TurboMainTask {
  title: string;
  description: string;
  subtasks: TurboSubtask[];
}

export interface TurboSubtask {
  id: string;
  description: string;
}

export interface TurboResult {
  enhanced_text: string;
  original_text: string;
  enhancement_applied: boolean;
  model_used?: string;
  tokens_used?: number;
  reasoning_content?: string;
  // Parsed content
  title: string;
  event_log: string;
  description: string;
  tasks: TurboMainTask[];
}

export class AITurboService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   * Supports both OpenAI and OpenRouter APIs
   */
  private initializeClient(): void {
    try {
      // Check for OpenRouter configuration first, then fallback to OpenAI
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      // Configure for OpenRouter or OpenAI
      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2'),
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_APP_NAME || 'Prosperous Codex'
          }
        });
      } else {
        this.client = new OpenAI({
          apiKey: openAiKey,
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2')
        });
      }

      this.isConfigured = true;

    } catch (error) {
      this.client = null;
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if the service is configured (has API key)
   */
  public isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get the current AI provider being used
   */
  public getProvider(): 'openrouter' | 'openai' | 'none' {
    if (!this.isConfigured) return 'none';
    return process.env.OPENROUTER_API_KEY ? 'openrouter' : 'openai';
  }

  /**
   * Check if a model supports reasoning/thinking capabilities
   */
  private supportsReasoning(model: string): boolean {
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17',
      'deepseek/deepseek-chat-v3-0324',
      'openai/o1-preview',
      'openai/o1-mini'
    ];
    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   * For turbo service, we use thinking_budget parameter for reasoning models
   */
  private getReasoningConfig(model: string) {
    if (this.supportsReasoning(model)) {
      return {
        thinking_budget: parseInt(process.env.AI_THINKING_BUDGET || '20000')
      };
    }
    return undefined;
  }

  /**
   * Build the combined system prompt for enhancement and parsing
   */
  private buildTurboSystemPrompt(): string {
    return `You are a specialized AI assistant that performs both text enhancement and structured parsing in a single operation.

**STEP 1: REASONING**
First, analyze the input text and plan your approach for both enhancement and parsing.

**STEP 2: ENHANCEMENT**
Enhance the input text following these guidelines:
- Transform brief task requests into comprehensive project briefings
- Add professional context and detailed explanations
- Include relevant background information and considerations
- Maintain the original intent while expanding on details
- Use professional, clear language appropriate for project management

**STEP 3: PARSING**
Parse the enhanced content into structured JSON format with these sections:
- title: A clear, descriptive project title
- eventLog: Background context and project history
- description: Detailed project description and objectives
- tasks: Array of main tasks with subtasks

**OUTPUT FORMAT:**
Provide your response in exactly this format:

<thinking>
[Your reasoning process here]
</thinking>

**ENHANCED TEXT:**
[Your enhanced version of the input text]

**JSON_SEPARATOR**

{
  "title": "Project Title",
  "eventLog": "Background and context",
  "description": "Detailed description",
  "tasks": [
    {
      "title": "Main Task Title",
      "description": "Task description",
      "subtasks": [
        {
          "id": "subtask-1",
          "description": "Subtask description"
        }
      ]
    }
  ]
}

**CRITICAL REQUIREMENTS:**
1. Always include the <thinking> tags with your reasoning
2. Always include "**ENHANCED TEXT:**" header before enhanced content
3. Always include "**JSON_SEPARATOR**" exactly as shown
4. Always provide valid JSON after the separator
5. Ensure all JSON strings are properly escaped
6. Use camelCase for JSON field names (title, eventLog, description, tasks)`;
  }

  /**
   * Enhance and parse content in a single operation
   */
  public async enhanceAndParse(
    inputText: string,
    options: TurboOptions = {}
  ): Promise<TurboResult> {
    if (!this.isAvailable()) {
      throw new Error('AI Turbo Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536')
    } = options;

    const systemPrompt = this.buildTurboSystemPrompt();
    const userPrompt = `Input text to enhance and parse: ${inputText}`;

    // Try primary model first, then fallback
    let completion;
    let modelUsed = this.primaryModel;

    try {
      // Build request configuration
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens,
        temperature: 0.65,
        top_p: 0.65,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);
    } catch (primaryError) {
      // Try fallback model
      try {
        modelUsed = this.fallbackModel;
        
        const fallbackConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens,
          temperature: 0.65,
          top_p: 0.65,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        };

        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackConfig);
      } catch (fallbackError) {
        throw primaryError; // Throw the original error
      }
    }

    try {
      const responseText = completion.choices[0]?.message?.content;

      if (!responseText) {
        throw new Error('No response received from AI service');
      }

      // Parse the combined response
      return this.parseTurboResponse(responseText, inputText, modelUsed, completion.usage?.total_tokens);

    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI turbo processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse the combined turbo response into structured result
   */
  private parseTurboResponse(
    responseText: string,
    originalText: string,
    modelUsed: string,
    tokensUsed?: number
  ): TurboResult {
    // Extract thinking content
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    const reasoningContent = thinkingMatch ? thinkingMatch[1].trim() : undefined;

    // Extract enhanced text
    const enhancedMatch = responseText.match(/\*\*ENHANCED TEXT:\*\*([\s\S]*?)\*\*JSON_SEPARATOR\*\*/);
    if (!enhancedMatch) {
      throw new Error('Could not extract enhanced text from response');
    }
    const enhancedText = enhancedMatch[1].trim();

    // Extract JSON content
    const jsonMatch = responseText.match(/\*\*JSON_SEPARATOR\*\*([\s\S]*?)$/);
    if (!jsonMatch) {
      throw new Error('Could not extract JSON content from response');
    }

    let parsedContent;
    try {
      parsedContent = JSON.parse(jsonMatch[1].trim());
    } catch (error) {
      throw new Error('Invalid JSON in response');
    }

    // Validate required fields
    if (!parsedContent.title || !parsedContent.description || !Array.isArray(parsedContent.tasks)) {
      throw new Error('Missing required fields in parsed content');
    }

    return {
      enhanced_text: enhancedText,
      original_text: originalText,
      enhancement_applied: true,
      model_used: modelUsed,
      tokens_used: tokensUsed,
      reasoning_content: reasoningContent,
      title: parsedContent.title,
      event_log: parsedContent.eventLog || '',
      description: parsedContent.description,
      tasks: parsedContent.tasks || []
    };
  }
}
