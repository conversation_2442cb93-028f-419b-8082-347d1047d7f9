import OpenAI from 'openai';

/**
 * AI Turbo Service
 * 
 * Combines AI enhancement and parsing into a single operation for improved efficiency.
 * Uses the same configuration patterns as existing AI services with enhanced reasoning support.
 */

export interface TurboOptions {
  max_tokens?: number;
  model?: string;
  context?: string;
  tone?: string;
}

export interface TurboMainTask {
  title: string;
  description: string;
  subtasks: TurboSubtask[];
}

export interface TurboSubtask {
  id: string;
  description: string;
}

export interface TurboResult {
  enhanced_text: string;
  original_text: string;
  enhancement_applied: boolean;
  model_used?: string;
  tokens_used?: number;
  reasoning_content?: string;
  // Parsed content
  title: string;
  event_log: string;
  description: string;
  tasks: TurboMainTask[];
}

export class AITurboService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   * Supports both OpenAI and OpenRouter APIs
   */
  private initializeClient(): void {
    try {
      // Check for OpenRouter configuration first, then fallback to OpenAI
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      // Configure for OpenRouter or OpenAI
      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2'),
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_APP_NAME || 'Prosperous Codex'
          }
        });
      } else {
        this.client = new OpenAI({
          apiKey: openAiKey,
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2')
        });
      }

      this.isConfigured = true;

    } catch (error) {
      this.client = null;
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if the service is configured (has API key)
   */
  public isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get the current AI provider being used
   */
  public getProvider(): 'openrouter' | 'openai' | 'none' {
    if (!this.isConfigured) return 'none';
    return process.env.OPENROUTER_API_KEY ? 'openrouter' : 'openai';
  }

  /**
   * Check if a model supports reasoning/thinking capabilities
   * Match the AI Enhancement Service configuration
   */
  private supportsReasoning(model: string): boolean {
    // Models that support reasoning with OpenRouter reasoning parameter
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17', // Primary model with reasoning support
      // Add other reasoning-capable models here as needed
    ];
    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   * Use the same configuration as AI Enhancement Service
   */
  private getReasoningConfig(model: string) {
    if (this.supportsReasoning(model)) {
      return {
        effort: 'high',
        exclude: false
      };
    }
    return undefined;
  }

  /**
   * Build the combined system prompt for enhancement and parsing
   * Uses the revised Prosperous Printing Company specific prompt
   */
  private buildTurboSystemPrompt(): string {
    return `**Prompt: AI Turbo Service - Unified Briefing Generation and JSON Parsing**

**Mandatory Protocol:** Your process must be three distinct steps, completed in a single response.
1. **Reasoning Step:** Encapsulate your entire reasoning process within a \`<thinking>...\</thinking>\` block. This block must contain ONLY your analysis and planning.
2. **Enhancement Step:** After closing the thinking block, generate the full, human-readable project briefing document based on your reasoning, following all guidelines in Phase 1.
3. **Parsing Step:** After generating the briefing, you must add a unique separator \`---JSON_SEPARATOR---\` on a new line, followed immediately by the final JSON object.

It is a critical failure to mix the content of these steps.

---

### **PHASE 1: ENHANCEMENT GUIDELINES (Internal Generation)**

You are a bilingual Project Management Assistant for Prosperous Printing Company Limited.

**Guiding Principles:**
- **Be Factual:** Base all output strictly on the provided input. Do not invent details.
- **Handle Vague Inputs:** If the input is too brief to create a meaningful briefing (e.g., "new website"), state this in the description and create a basic task structure for the user to fill out. Do not hallucinate.
- **Language Detection:** If the user's input text contains more than 33% Chinese characters, your entire response must be in Traditional Chinese. Otherwise, it must be in English.

**Terminology (for Traditional Chinese output):**
| English Term | Traditional Chinese Term |
| :--- | :--- |
| Subject | 主題 |
| Project | 項目 |
| Key Focus | 核心重點 |
| Event Background | 事件背景 |
| Core Problem | 核心問題 |
| Top Priority | 首要任務 |

**Enhancement Output Structure:**
- **Subject:** Project Briefing: [Project Name] // [Client Name]
- The section markers (\`---EventLog---\`, \`---Description---\`, \`---TaskAssignment---\`, and their corresponding end markers) MUST be output exactly as written and must NOT be translated.

---EventLog---
[Content generated according to Event Log rules]
---EndOfEventLog---

---Description---
[Content generated according to Description rules]
---EndOfDescription---

---TaskAssignment---
[Content generated according to Task Assignment rules]
---EndOfTaskAssignment---

**Detailed Content Generation Rules:**

* **Event Log Rules:**
  * Read the \`---EmailThread---\` and any existing \`---EventLog---\`.
  * Summarize new, material events concisely (e.g., "Requested files for 7 books" not the full list).
  * Combine and output the complete log chronologically.
  * Each entry must start with a dash (-).
  * Use concise, common-sense short names for affiliations (e.g., 'Prosperous' for 'Prosperous Printing Company Limited').
  * Format: \`- DD/MM/YYYY - [Sender Name] ([Affiliation]): [Summary]\`

* **Description Rules:**
  * Synthesize all input into a detailed narrative.
  * Use paragraphs for the main narrative, but use indented bulleted lists (\`-\`) for multiple issues or requirements.
  * The narrative must cover: Project's overall goal, summary of how the situation evolved, detailed explanation of the core problem/trigger, and a clear statement of the immediate objective.

* **Task Assignment Rules:**
  * The Task Title must be a concise, action-oriented phrase that summarizes the core action (e.g., "Debug Data Refresh Failure").
  * Do NOT assign tasks to anyone.
  * Use an indented structure for visual hierarchy.
  * Write a brief Description for each main task explaining its goal and importance.
  * List subtasks using Task.Subtask numbering (e.g., 1.1, 1.2).
  * Provide a brief Description for each subtask explaining the specific action required.

---

### **PHASE 2: JSON GENERATION REQUIREMENTS**

After generating the complete enhanced text from Phase 1, add the separator and then generate a single, valid JSON object that parses the text you just wrote.

**JSON Schema:**
\`\`\`json
{
  "title": "string",
  "eventLog": "string",
  "description": "string",
  "tasks": [
    {
      "title": "string",
      "description": "string",
      "subtasks": [
        {
          "id": "string",
          "description": "string"
        }
      ]
    }
  ]
}
\`\`\`

**JSON Parsing Rules:**
1. **title**: Extract from the "Subject:" line.
2. **eventLog**: Extract the pure content between \`---EventLog---\` and \`---EndOfEventLog---\`.
3. **description**: Extract the pure content between \`---Description---\` and \`---EndOfDescription---\`.
4. **tasks**: Parse the content between \`---TaskAssignment---\` and \`---EndOfTaskAssignment---\`. Preserve all numbering for task and subtask titles/IDs.`;
  }

  /**
   * Enhance and parse content in a single operation
   */
  public async enhanceAndParse(
    inputText: string,
    options: TurboOptions = {}
  ): Promise<TurboResult> {
    if (!this.isAvailable()) {
      throw new Error('AI Turbo Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536')
    } = options;

    const systemPrompt = this.buildTurboSystemPrompt();
    const userPrompt = `--- EVENT LOG ---
None

--- EMAIL THREAD ---
Task Request: ${inputText}`;

    // Try primary model first, then fallback
    let completion;
    let modelUsed = this.primaryModel;

    try {
      // Build request configuration
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens,
        temperature: 0.65,
        top_p: 0.65,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);
    } catch (primaryError) {
      // Try fallback model
      try {
        modelUsed = this.fallbackModel;
        
        const fallbackConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens,
          temperature: 0.65,
          top_p: 0.65,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        };

        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackConfig);
      } catch (fallbackError) {
        throw primaryError; // Throw the original error
      }
    }

    try {
      const responseText = completion.choices[0]?.message?.content;

      if (!responseText) {
        throw new Error('No response received from AI service');
      }

      // Temporary debugging - remove after fixing
      console.log('=== AI TURBO RAW RESPONSE DEBUG ===');
      console.log('Response length:', responseText.length);
      console.log('Has thinking tags:', responseText.includes('<thinking>'));
      console.log('Has JSON_SEPARATOR:', responseText.includes('---JSON_SEPARATOR---'));
      console.log('Has EventLog markers:', responseText.includes('---EventLog---'));
      console.log('Has Description markers:', responseText.includes('---Description---'));
      console.log('Has TaskAssignment markers:', responseText.includes('---TaskAssignment---'));
      console.log('First 500 chars:', responseText.substring(0, 500));
      console.log('Last 500 chars:', responseText.substring(responseText.length - 500));
      console.log('=== END DEBUG ===');

      // Parse the combined response
      return this.parseTurboResponse(responseText, inputText, modelUsed, completion.usage?.total_tokens);

    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI turbo processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse the combined turbo response into structured result
   * Handles the new structured format with section markers
   */
  private parseTurboResponse(
    responseText: string,
    originalText: string,
    modelUsed: string,
    tokensUsed?: number
  ): TurboResult {
    console.log('=== PARSING TURBO RESPONSE ===');

    // Extract thinking content
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    const reasoningContent = thinkingMatch ? thinkingMatch[1].trim() : undefined;
    console.log('Thinking content found:', !!reasoningContent);

    // Extract enhanced text (everything between </thinking> and ---JSON_SEPARATOR---)
    const afterThinking = responseText.replace(/<thinking>[\s\S]*?<\/thinking>/g, '').trim();
    let enhancedText = '';

    const enhancedMatch = afterThinking.match(/([\s\S]*?)\s*---JSON_SEPARATOR---/i);
    if (enhancedMatch) {
      enhancedText = enhancedMatch[1].trim();
      console.log('Enhanced text extracted, length:', enhancedText.length);
    } else {
      // Fallback: use everything after thinking if no separator found
      enhancedText = afterThinking;
      console.log('Using fallback enhanced text extraction');
    }

    // Extract and parse JSON content
    let jsonMatch = responseText.match(/---JSON_SEPARATOR---\s*([\s\S]*?)$/i);
    if (!jsonMatch) {
      throw new Error('Could not find ---JSON_SEPARATOR--- in response');
    }

    let jsonContent = jsonMatch[1].trim();
    // Clean up markdown code blocks if present
    jsonContent = jsonContent.replace(/^```json\s*/i, '').replace(/\s*```$/, '').trim();

    console.log('JSON content to parse:', jsonContent.substring(0, 200) + '...');

    let parsedContent;
    try {
      parsedContent = JSON.parse(jsonContent);
      console.log('JSON parsed successfully, keys:', Object.keys(parsedContent));
    } catch (error) {
      console.error('JSON parsing failed:', error);
      console.error('Raw JSON content:', jsonContent);
      throw new Error(`Invalid JSON in response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate and extract fields
    const title = parsedContent.title || 'Untitled Project';
    const eventLog = parsedContent.eventLog || '';
    const description = parsedContent.description || originalText; // Fallback to original if no description
    const tasks = Array.isArray(parsedContent.tasks) ? parsedContent.tasks : [];

    console.log('Parsed fields:', {
      title: title.substring(0, 50) + '...',
      eventLogLength: eventLog.length,
      descriptionLength: description.length,
      tasksCount: tasks.length
    });

    // If we have structured content but empty fields, try to extract from enhanced text
    if (!eventLog && !description && enhancedText) {
      console.log('Attempting to extract structured content from enhanced text...');

      // Try to extract from section markers in enhanced text
      const extractedData = this.extractFromSectionMarkers(enhancedText);

      return {
        enhanced_text: enhancedText,
        original_text: originalText,
        enhancement_applied: true,
        model_used: modelUsed,
        tokens_used: tokensUsed,
        reasoning_content: reasoningContent,
        title: extractedData.title || title,
        event_log: extractedData.eventLog || eventLog,
        description: extractedData.description || description,
        tasks: extractedData.tasks.length > 0 ? extractedData.tasks : tasks
      };
    }

    console.log('=== PARSING COMPLETE ===');

    return {
      enhanced_text: enhancedText,
      original_text: originalText,
      enhancement_applied: true,
      model_used: modelUsed,
      tokens_used: tokensUsed,
      reasoning_content: reasoningContent,
      title: title,
      event_log: eventLog,
      description: description,
      tasks: tasks
    };
  }

  /**
   * Extract structured content from section markers in enhanced text
   */
  private extractFromSectionMarkers(enhancedText: string): {
    title: string;
    eventLog: string;
    description: string;
    tasks: any[];
  } {
    console.log('Extracting from section markers...');

    // Extract title from Subject line
    const titleMatch = enhancedText.match(/Subject:\s*(.+)/i);
    const title = titleMatch ? titleMatch[1].trim() : '';

    // Extract event log
    const eventLogMatch = enhancedText.match(/---EventLog---([\s\S]*?)---EndOfEventLog---/i);
    const eventLog = eventLogMatch ? eventLogMatch[1].trim() : '';

    // Extract description
    const descriptionMatch = enhancedText.match(/---Description---([\s\S]*?)---EndOfDescription---/i);
    const description = descriptionMatch ? descriptionMatch[1].trim() : '';

    // Extract and parse tasks
    const taskAssignmentMatch = enhancedText.match(/---TaskAssignment---([\s\S]*?)---EndOfTaskAssignment---/i);
    const tasks = taskAssignmentMatch ? this.parseTaskAssignment(taskAssignmentMatch[1].trim()) : [];

    console.log('Extracted from markers:', {
      title: title.substring(0, 50) + '...',
      eventLogLength: eventLog.length,
      descriptionLength: description.length,
      tasksCount: tasks.length
    });

    return { title, eventLog, description, tasks };
  }

  /**
   * Parse task assignment section into structured tasks
   */
  private parseTaskAssignment(taskText: string): any[] {
    const tasks: any[] = [];

    // Split by main task patterns (look for numbered tasks)
    const taskSections = taskText.split(/(?=^\d+\.\s)/m);

    for (const section of taskSections) {
      if (!section.trim()) continue;

      const lines = section.trim().split('\n');
      const firstLine = lines[0];

      // Extract main task number and title
      const mainTaskMatch = firstLine.match(/^(\d+)\.\s*(.+)/);
      if (!mainTaskMatch) continue;

      const taskNumber = mainTaskMatch[1];
      const taskTitle = mainTaskMatch[2].trim();

      // Find description and subtasks
      let description = '';
      const subtasks: any[] = [];

      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();

        // Check if it's a subtask (e.g., "1.1", "1.2")
        const subtaskMatch = line.match(/^(\d+\.\d+)\s*(.+)/);
        if (subtaskMatch) {
          subtasks.push({
            id: subtaskMatch[1],
            description: subtaskMatch[2].trim()
          });
        } else if (line && !line.startsWith('Description:')) {
          // Add to description if it's not empty and not a "Description:" label
          if (description) description += ' ';
          description += line;
        }
      }

      tasks.push({
        title: taskTitle,
        description: description || `Task ${taskNumber} description`,
        subtasks: subtasks
      });
    }

    return tasks;
  }
}
