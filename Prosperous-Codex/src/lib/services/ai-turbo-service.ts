import OpenAI from 'openai';

/**
 * AI Turbo Service
 * 
 * Combines AI enhancement and parsing into a single operation for improved efficiency.
 * Uses the same configuration patterns as existing AI services with enhanced reasoning support.
 */

export interface TurboOptions {
  max_tokens?: number;
  model?: string;
  context?: string;
  tone?: string;
}

export interface TurboMainTask {
  title: string;
  description: string;
  subtasks: TurboSubtask[];
}

export interface TurboSubtask {
  id: string;
  description: string;
}

export interface TurboResult {
  enhanced_text: string;
  original_text: string;
  enhancement_applied: boolean;
  model_used?: string;
  tokens_used?: number;
  reasoning_content?: string;
  // Parsed content
  title: string;
  event_log: string;
  description: string;
  tasks: TurboMainTask[];
}

export class AITurboService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   * Supports both OpenAI and OpenRouter APIs
   */
  private initializeClient(): void {
    try {
      // Check for OpenRouter configuration first, then fallback to OpenAI
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      // Configure for OpenRouter or OpenAI
      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2'),
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_APP_NAME || 'Prosperous Codex'
          }
        });
      } else {
        this.client = new OpenAI({
          apiKey: openAiKey,
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2')
        });
      }

      this.isConfigured = true;

    } catch (error) {
      this.client = null;
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if the service is configured (has API key)
   */
  public isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get the current AI provider being used
   */
  public getProvider(): 'openrouter' | 'openai' | 'none' {
    if (!this.isConfigured) return 'none';
    return process.env.OPENROUTER_API_KEY ? 'openrouter' : 'openai';
  }

  /**
   * Check if a model supports reasoning/thinking capabilities
   * Match the AI Enhancement Service configuration
   */
  private supportsReasoning(model: string): boolean {
    // Models that support reasoning with OpenRouter reasoning parameter
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17', // Primary model with reasoning support
      // Add other reasoning-capable models here as needed
    ];
    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   * Use the same configuration as AI Enhancement Service
   */
  private getReasoningConfig(model: string) {
    if (this.supportsReasoning(model)) {
      return {
        effort: 'high',
        exclude: false
      };
    }
    return undefined;
  }

  /**
   * Build the combined system prompt for enhancement and parsing
   * Uses the revised Prosperous Printing Company specific prompt
   */
  private buildTurboSystemPrompt(): string {
    return `**Prompt: AI Turbo Service - Unified Briefing Generation and JSON Parsing**

**Mandatory Protocol:** Your process must be three distinct steps, completed in a single response.
1. **Reasoning Step:** Encapsulate your entire reasoning process within a \`<thinking>...\</thinking>\` block. This block must contain ONLY your analysis and planning.
2. **Enhancement Step:** After closing the thinking block, generate the full, human-readable project briefing document based on your reasoning, following all guidelines in Phase 1.
3. **Parsing Step:** After generating the briefing, you must add a unique separator \`---JSON_SEPARATOR---\` on a new line, followed immediately by the final JSON object.

It is a critical failure to mix the content of these steps.

---

### **PHASE 1: ENHANCEMENT GUIDELINES (Internal Generation)**

You are a bilingual Project Management Assistant for Prosperous Printing Company Limited.

**Guiding Principles:**
- **Be Factual:** Base all output strictly on the provided input. Do not invent details.
- **Handle Vague Inputs:** If the input is too brief to create a meaningful briefing (e.g., "new website"), state this in the description and create a basic task structure for the user to fill out. Do not hallucinate.
- **Language Detection:** If the user's input text contains more than 33% Chinese characters, your entire response must be in Traditional Chinese. Otherwise, it must be in English.

**Terminology (for Traditional Chinese output):**
| English Term | Traditional Chinese Term |
| :--- | :--- |
| Subject | 主題 |
| Project | 項目 |
| Key Focus | 核心重點 |
| Event Background | 事件背景 |
| Core Problem | 核心問題 |
| Top Priority | 首要任務 |

**Enhancement Output Structure:**
- **Subject:** Project Briefing: [Project Name] // [Client Name]
- The section markers (\`---EventLog---\`, \`---Description---\`, \`---TaskAssignment---\`, and their corresponding end markers) MUST be output exactly as written and must NOT be translated.

---EventLog---
[Content generated according to Event Log rules]
---EndOfEventLog---

---Description---
[Content generated according to Description rules]
---EndOfDescription---

---TaskAssignment---
[Content generated according to Task Assignment rules]
---EndOfTaskAssignment---

**Detailed Content Generation Rules:**

* **Event Log Rules:**
  * Read the \`---EmailThread---\` and any existing \`---EventLog---\`.
  * Summarize new, material events concisely (e.g., "Requested files for 7 books" not the full list).
  * Combine and output the complete log chronologically.
  * Each entry must start with a dash (-).
  * Use concise, common-sense short names for affiliations (e.g., 'Prosperous' for 'Prosperous Printing Company Limited').
  * Format: \`- DD/MM/YYYY - [Sender Name] ([Affiliation]): [Summary]\`

* **Description Rules:**
  * Synthesize all input into a detailed narrative.
  * Use paragraphs for the main narrative, but use indented bulleted lists (\`-\`) for multiple issues or requirements.
  * The narrative must cover: Project's overall goal, summary of how the situation evolved, detailed explanation of the core problem/trigger, and a clear statement of the immediate objective.

* **Task Assignment Rules:**
  * The Task Title must be a concise, action-oriented phrase that summarizes the core action (e.g., "Debug Data Refresh Failure").
  * Do NOT assign tasks to anyone.
  * Use an indented structure for visual hierarchy.
  * Write a brief Description for each main task explaining its goal and importance.
  * List subtasks using Task.Subtask numbering (e.g., 1.1, 1.2).
  * Provide a brief Description for each subtask explaining the specific action required.

---

### **PHASE 2: JSON GENERATION REQUIREMENTS**

After generating the complete enhanced text from Phase 1, add the separator and then generate a single, valid JSON object that parses the text you just wrote.

**JSON Schema:**
\`\`\`json
{
  "title": "string",
  "eventLog": "string",
  "description": "string",
  "tasks": [
    {
      "title": "string",
      "description": "string",
      "subtasks": [
        {
          "id": "string",
          "description": "string"
        }
      ]
    }
  ]
}
\`\`\`

**JSON Parsing Rules:**
1. **title**: Extract from the "Subject:" line.
2. **eventLog**: Extract the pure content between \`---EventLog---\` and \`---EndOfEventLog---\`.
3. **description**: Extract the pure content between \`---Description---\` and \`---EndOfDescription---\`.
4. **tasks**: Parse the content between \`---TaskAssignment---\` and \`---EndOfTaskAssignment---\`. Preserve all numbering for task and subtask titles/IDs.`;
  }

  /**
   * Enhance and parse content in a single operation
   */
  public async enhanceAndParse(
    inputText: string,
    options: TurboOptions = {}
  ): Promise<TurboResult> {
    if (!this.isAvailable()) {
      throw new Error('AI Turbo Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536')
    } = options;

    const systemPrompt = this.buildTurboSystemPrompt();
    const userPrompt = `--- EVENT LOG ---
None

--- EMAIL THREAD ---
Task Request: ${inputText}`;

    // Try primary model first, then fallback
    let completion;
    let modelUsed = this.primaryModel;

    try {
      // Build request configuration - match AI Enhancement Service exactly
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens, // Same as enhancement service: parseInt(process.env.AI_MAX_TOKENS || '65536')
        temperature: 0.65,      // Same as enhancement service
        top_p: 0.65,           // Same as enhancement service
        frequency_penalty: 0.1, // Same as enhancement service
        presence_penalty: 0.1   // Same as enhancement service
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);
    } catch (primaryError) {
      // Try fallback model
      try {
        modelUsed = this.fallbackModel;
        
        const fallbackConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens, // Same as enhancement service
          temperature: 0.65,      // Same as enhancement service
          top_p: 0.65,           // Same as enhancement service
          frequency_penalty: 0.1, // Same as enhancement service
          presence_penalty: 0.1   // Same as enhancement service
        };

        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackConfig);
      } catch (fallbackError) {
        throw primaryError; // Throw the original error
      }
    }

    try {
      console.log('=== AI TURBO RESPONSE DEBUG ===');
      console.log('Completion object:', !!completion);
      console.log('Choices array:', completion?.choices?.length || 0);
      console.log('First choice:', !!completion?.choices?.[0]);
      console.log('Message object:', !!completion?.choices?.[0]?.message);
      console.log('Content exists:', !!completion?.choices?.[0]?.message?.content);

      const responseText = completion.choices[0]?.message?.content;

      if (!responseText) {
        console.error('No response content found');
        console.error('Full completion object:', JSON.stringify(completion, null, 2));
        throw new Error('No response received from AI service');
      }

      console.log('Response length:', responseText.length);
      console.log('Response preview:', responseText.substring(0, 200));
      console.log('=== END DEBUG ===');

      // For now, return just the enhanced text like the Enhance button
      // This allows manual inspection before parsing
      return {
        enhanced_text: responseText,
        original_text: inputText,
        enhancement_applied: true,
        model_used: modelUsed,
        tokens_used: completion.usage?.total_tokens,
        reasoning_content: this.extractThinkingContent(responseText),
        title: 'Debug Mode - Manual Inspection Required',
        event_log: '',
        description: responseText, // Put the full response in description for inspection
        tasks: []
      };

    } catch (error) {
      console.error('=== AI TURBO ERROR DEBUG ===');
      console.error('Error type:', error?.constructor?.name);
      console.error('Error message:', error instanceof Error ? error.message : String(error));
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack');
      console.error('=== END ERROR DEBUG ===');

      if (error instanceof OpenAI.APIError) {
        console.error('OpenAI API Error Details:', {
          status: error.status,
          message: error.message,
          type: error.type,
          code: error.code
        });

        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      throw new Error(`AI turbo processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract thinking content from response text
   */
  private extractThinkingContent(responseText: string): string | undefined {
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    return thinkingMatch ? thinkingMatch[1].trim() : undefined;
  }

  /**
   * Parse the combined turbo response into structured result
   * Handles the new structured format with section markers
   */
  private async parseTurboResponse(
    responseText: string,
    originalText: string,
    modelUsed: string,
    tokensUsed?: number
  ): Promise<TurboResult> {
    // Extract thinking content
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    const reasoningContent = thinkingMatch ? thinkingMatch[1].trim() : undefined;

    // Extract enhanced text (everything between </thinking> and ---JSON_SEPARATOR---)
    const afterThinking = responseText.replace(/<thinking>[\s\S]*?<\/thinking>/g, '').trim();
    let enhancedText = '';

    const enhancedMatch = afterThinking.match(/([\s\S]*?)\s*---JSON_SEPARATOR---/i);
    if (enhancedMatch) {
      enhancedText = enhancedMatch[1].trim();
    } else {
      // Fallback: use everything after thinking if no separator found
      enhancedText = afterThinking;
    }

    // Extract and parse JSON content
    let jsonMatch = responseText.match(/---JSON_SEPARATOR---\s*([\s\S]*?)$/i);
    if (!jsonMatch) {
      throw new Error('Could not find ---JSON_SEPARATOR--- in response');
    }

    let jsonContent = jsonMatch[1].trim();
    // Clean up markdown code blocks if present
    jsonContent = jsonContent.replace(/^```json\s*/i, '').replace(/\s*```$/, '').trim();

    let parsedContent;
    try {
      parsedContent = JSON.parse(jsonContent);
    } catch (error) {
      throw new Error(`Invalid JSON in response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate and extract fields
    const title = parsedContent.title || 'Untitled Project';
    const eventLog = parsedContent.eventLog || '';
    const description = parsedContent.description || originalText; // Fallback to original if no description
    const tasks = Array.isArray(parsedContent.tasks) ? parsedContent.tasks : [];

    // If we have structured content but empty fields, try to extract from enhanced text
    if ((!eventLog && !description) || tasks.length === 0) {
      // Try to extract from section markers in enhanced text
      const extractedData = this.extractFromSectionMarkers(enhancedText);

      // If tasks are still empty, try using the Parse button's AI service as fallback
      let finalTasks = extractedData.tasks;
      if (finalTasks.length === 0 && enhancedText) {
        finalTasks = await this.fallbackParseWithAIService(enhancedText);
      }

      return {
        enhanced_text: enhancedText,
        original_text: originalText,
        enhancement_applied: true,
        model_used: modelUsed,
        tokens_used: tokensUsed,
        reasoning_content: reasoningContent,
        title: extractedData.title || title,
        event_log: extractedData.eventLog || eventLog,
        description: extractedData.description || description,
        tasks: finalTasks.length > 0 ? finalTasks : tasks
      };
    }

    return {
      enhanced_text: enhancedText,
      original_text: originalText,
      enhancement_applied: true,
      model_used: modelUsed,
      tokens_used: tokensUsed,
      reasoning_content: reasoningContent,
      title: title,
      event_log: eventLog,
      description: description,
      tasks: tasks
    };
  }

  /**
   * Extract structured content from section markers in enhanced text
   */
  private extractFromSectionMarkers(enhancedText: string): {
    title: string;
    eventLog: string;
    description: string;
    tasks: any[];
  } {
    // Extract title from Subject line
    const titleMatch = enhancedText.match(/Subject:\s*(.+)/i);
    const title = titleMatch ? titleMatch[1].trim() : '';

    // Extract event log
    const eventLogMatch = enhancedText.match(/---EventLog---([\s\S]*?)---EndOfEventLog---/i);
    const eventLog = eventLogMatch ? eventLogMatch[1].trim() : '';

    // Extract description
    const descriptionMatch = enhancedText.match(/---Description---([\s\S]*?)---EndOfDescription---/i);
    const description = descriptionMatch ? descriptionMatch[1].trim() : '';

    // Extract and parse tasks
    const taskAssignmentMatch = enhancedText.match(/---TaskAssignment---([\s\S]*?)---EndOfTaskAssignment---/i);
    const tasks = taskAssignmentMatch ? this.parseTaskAssignment(taskAssignmentMatch[1].trim()) : [];

    return { title, eventLog, description, tasks };
  }

  /**
   * Parse task assignment section using the same logic as the proven Parse button
   * This replicates the exact parsing logic from ai-parsing-service.ts
   */
  private parseTaskAssignment(taskText: string): any[] {
    // Use the same parsing logic as the AI Parsing Service
    // Create a temporary input that matches what the Parse button expects
    const tempInput = `---TaskAssignment---\n${taskText}\n---EndOfTaskAssignment---`;

    // Parse using the same pattern matching as the Parse button
    return this.parseTasksLikeParseButton(tempInput);
  }

  /**
   * Replicate the exact task parsing logic from the Parse button's AI Parsing Service
   */
  private parseTasksLikeParseButton(inputText: string): any[] {
    const tasks: any[] = [];

    // Extract task assignment section (same as Parse button)
    const taskMatch = inputText.match(/---TaskAssignment---([\s\S]*?)---EndOfTaskAssignment---/i);
    if (!taskMatch) {
      return tasks;
    }

    const taskContent = taskMatch[1].trim();
    const lines = taskContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    let currentTask: any = null;

    for (const line of lines) {
      // Check for main task patterns (same as Parse button logic)
      // MAIN TASKS: Lines that start with major indicators like "»", "Task", or numbered items without decimal points
      const mainTaskPatterns = [
        /^»\s*(.+)$/,           // Lines starting with »
        /^Task\s*\d*:?\s*(.+)$/i, // Lines starting with "Task"
        /^(\d+)\.\s*(.+)$/,     // Numbered items (1., 2., 3.)
        /^[-•]\s*(.+)$/         // Bullet points
      ];

      let isMainTask = false;
      let taskTitle = '';

      for (const pattern of mainTaskPatterns) {
        const match = line.match(pattern);
        if (match) {
          isMainTask = true;
          taskTitle = match[1] || match[0]; // Use full match if no capture group
          break;
        }
      }

      if (isMainTask) {
        // Save previous task if exists
        if (currentTask) {
          tasks.push(currentTask);
        }

        // Start new main task
        currentTask = {
          title: line, // Keep the full line including markers (same as Parse button)
          description: taskTitle, // Clean description
          subtasks: []
        };
      } else {
        // Check for subtask patterns (same as Parse button logic)
        // SUBTASKS: Lines that have decimal numbering (1.1, 1.2, 2.1, etc.)
        const subtaskMatch = line.match(/^(\d+\.\d+)\s*(.+)$/);
        if (subtaskMatch && currentTask) {
          currentTask.subtasks.push({
            id: subtaskMatch[1],
            description: subtaskMatch[2]
          });
        } else if (line && currentTask && !currentTask.description) {
          // If no description yet, use this line as description
          currentTask.description = line;
        }
      }
    }

    // Add the last task
    if (currentTask) {
      tasks.push(currentTask);
    }

    return tasks;
  }

  /**
   * Fallback method: Use the Parse button's AI service to parse tasks
   * This ensures we get the exact same task parsing as the proven Parse button
   */
  private async fallbackParseWithAIService(enhancedText: string): Promise<any[]> {
    try {
      // Import the AI Parsing Service
      const { aiParsingService } = await import('@/lib/services/ai-parsing-service');

      if (!aiParsingService.isAvailable()) {
        console.log('AI Parsing Service not available for fallback');
        return [];
      }

      // Use the Parse button's service to parse the enhanced text
      const parsedContent = await aiParsingService.parseStructuredContent(enhancedText);
      return parsedContent.tasks || [];

    } catch (error) {
      return [];
    }
  }
}
