import OpenAI from 'openai';

/**
 * AI Turbo Service
 * 
 * Combines AI enhancement and parsing into a single operation for improved efficiency.
 * Uses the same configuration patterns as existing AI services with enhanced reasoning support.
 */

export interface TurboOptions {
  max_tokens?: number;
  model?: string;
  context?: string;
  tone?: string;
}

export interface TurboMainTask {
  title: string;
  description: string;
  subtasks: TurboSubtask[];
}

export interface TurboSubtask {
  id: string;
  description: string;
}

export interface TurboResult {
  enhanced_text: string;
  original_text: string;
  enhancement_applied: boolean;
  model_used?: string;
  tokens_used?: number;
  reasoning_content?: string;
  // Parsed content
  title: string;
  event_log: string;
  description: string;
  tasks: TurboMainTask[];
}

export class AITurboService {
  private client: OpenAI | null = null;
  private isConfigured: boolean = false;
  private readonly primaryModel = 'google/gemini-2.5-flash-lite-preview-06-17';
  private readonly fallbackModel = 'deepseek/deepseek-chat-v3-0324';

  constructor() {
    this.initializeClient();
  }

  /**
   * Initialize OpenAI-compatible client with environment configuration
   * Supports both OpenAI and OpenRouter APIs
   */
  private initializeClient(): void {
    try {
      // Check for OpenRouter configuration first, then fallback to OpenAI
      const openRouterKey = process.env.OPENROUTER_API_KEY;
      const openAiKey = process.env.OPENAI_API_KEY;

      if (!openRouterKey && !openAiKey) {
        return;
      }

      // Configure for OpenRouter or OpenAI
      if (openRouterKey) {
        this.client = new OpenAI({
          apiKey: openRouterKey,
          baseURL: 'https://openrouter.ai/api/v1',
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2'),
          defaultHeaders: {
            'HTTP-Referer': process.env.OPENROUTER_REFERER || 'http://localhost:3000',
            'X-Title': process.env.OPENROUTER_APP_NAME || 'Prosperous Codex'
          }
        });
      } else {
        this.client = new OpenAI({
          apiKey: openAiKey,
          timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.AI_MAX_RETRIES || '2')
        });
      }

      this.isConfigured = true;

    } catch (error) {
      this.client = null;
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is available and properly configured
   */
  public isAvailable(): boolean {
    return this.client !== null && this.isConfigured;
  }

  /**
   * Check if the service is configured (has API key)
   */
  public isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get the current AI provider being used
   */
  public getProvider(): 'openrouter' | 'openai' | 'none' {
    if (!this.isConfigured) return 'none';
    return process.env.OPENROUTER_API_KEY ? 'openrouter' : 'openai';
  }

  /**
   * Check if a model supports reasoning/thinking capabilities
   * Match the AI Enhancement Service configuration
   */
  private supportsReasoning(model: string): boolean {
    // Models that support reasoning with OpenRouter reasoning parameter
    const reasoningModels = [
      'google/gemini-2.5-flash-lite-preview-06-17', // Primary model with reasoning support
      // Add other reasoning-capable models here as needed
    ];
    return reasoningModels.includes(model);
  }

  /**
   * Get reasoning configuration for models that support it
   * Use the same configuration as AI Enhancement Service
   */
  private getReasoningConfig(model: string) {
    if (this.supportsReasoning(model)) {
      return {
        effort: 'high',
        exclude: false
      };
    }
    return undefined;
  }

  /**
   * Build the combined system prompt for enhancement and parsing
   * Uses the same enhancement prompt as AIEnhancementService plus parsing instructions
   */
  private buildTurboSystemPrompt(): string {
    // Use the exact same enhancement prompt as AIEnhancementService
    const enhancementPrompt = `**Prompt: Detailed Project Briefing Assistant**

**Role**: You are a specialized project briefing assistant designed to transform brief task requests into comprehensive, professional project briefings. Your primary function is to take minimal input and expand it into detailed, actionable project documentation that provides clear context, scope, and direction.

**Core Responsibilities**:

1. **Context Expansion**: Transform brief requests into comprehensive project briefings with relevant background information
2. **Professional Enhancement**: Elevate casual language to professional project management terminology
3. **Scope Clarification**: Identify and articulate project boundaries, deliverables, and success criteria
4. **Stakeholder Consideration**: Anticipate and address potential stakeholder concerns and requirements
5. **Risk Assessment**: Identify potential challenges and suggest mitigation strategies
6. **Resource Planning**: Consider resource requirements and timeline implications

**Enhancement Guidelines**:

- **Maintain Original Intent**: Never alter the core request or desired outcome
- **Add Professional Context**: Provide industry-standard project management framing
- **Include Background Information**: Add relevant context that stakeholders would need
- **Specify Deliverables**: Clearly articulate what will be produced or achieved
- **Consider Dependencies**: Identify potential prerequisites or related work
- **Address Quality Standards**: Include appropriate quality criteria and acceptance criteria
- **Timeline Awareness**: Consider realistic timeframes and milestones
- **Communication Planning**: Suggest appropriate stakeholder communication approaches

**Output Requirements**:

- **Professional Tone**: Use clear, professional language appropriate for business communication
- **Structured Content**: Organize information logically with clear sections
- **Actionable Details**: Provide specific, implementable guidance
- **Comprehensive Coverage**: Address all relevant aspects of the project
- **Stakeholder Focus**: Consider the needs of different stakeholder groups
- **Risk Awareness**: Acknowledge potential challenges and suggest approaches

**Quality Standards**:

- **Clarity**: All content must be easily understood by stakeholders
- **Completeness**: Cover all essential aspects of the project
- **Accuracy**: Ensure all suggestions are realistic and achievable
- **Relevance**: Focus on information that adds value to the project
- **Consistency**: Maintain consistent terminology and approach throughout

**Thinking Process Guidelines**:

You must use <thinking> tags to show your reasoning process. Your thinking should include:

1. **Analysis of the original request**
2. **Identification of gaps and areas for enhancement**
3. **Consideration of stakeholder needs and perspectives**
4. **Planning of the enhanced response structure**
5. **Quality review of your proposed enhancements**

**Critical Formatting Requirements**:

- Always begin with <thinking> tags containing your analysis
- Provide enhanced content after the thinking section
- Use professional project management language
- Structure content logically and clearly
- End with </thinking> tags for proper closure

**Validation Checklist**:

Before finalizing your response, verify:

- Original intent is preserved and enhanced
- Professional language is used throughout
- All relevant project aspects are addressed
- Content is actionable and specific
- Stakeholder needs are considered
- Quality standards are met
- Thinking process is clearly documented

**Meta-Validation Requirements**:

Ensure the following technical requirements are met:

First <thinking> tag is present at the start: Yes/No
Second <thinking> tag is present on the next line: Yes/No
First </thinking> tag (visual/fallback) is present at the end: Yes/No
Second </thinking> tag (functional) is present on the final line: Yes/No
Strict spacing requirements for closing tags are adhered to: Yes/No
Finalizing reasoning and preparing to generate output: Yes/No
Crucially, ensure the final user-facing output is clean and contains no meta-commentary or mention of this checklist or the reasoning process itself.

**ADDITIONAL TURBO MODE INSTRUCTIONS**:

After providing the enhanced text, you must also parse it into structured JSON format. Follow this exact format:

**JSON_SEPARATOR**

{
  "title": "Clear, descriptive project title",
  "eventLog": "Background context and project history",
  "description": "Detailed project description and objectives",
  "tasks": [
    {
      "title": "Main Task Title",
      "description": "Task description",
      "subtasks": [
        {
          "id": "subtask-1",
          "description": "Subtask description"
        }
      ]
    }
  ]
}

**CRITICAL TURBO REQUIREMENTS:**
1. Always include the <thinking> tags with your reasoning
2. Provide enhanced text using the full enhancement guidelines above
3. Always include "**JSON_SEPARATOR**" exactly as shown after the enhanced text
4. Always provide valid JSON after the separator
5. Ensure all JSON strings are properly escaped
6. Use camelCase for JSON field names (title, eventLog, description, tasks)
7. Extract meaningful tasks and subtasks from the enhanced content`;

    return enhancementPrompt;
  }

  /**
   * Enhance and parse content in a single operation
   */
  public async enhanceAndParse(
    inputText: string,
    options: TurboOptions = {}
  ): Promise<TurboResult> {
    if (!this.isAvailable()) {
      throw new Error('AI Turbo Service is not available. Please check API key configuration.');
    }

    const {
      max_tokens = parseInt(process.env.AI_MAX_TOKENS || '65536')
    } = options;

    const systemPrompt = this.buildTurboSystemPrompt();
    const userPrompt = `--- EVENT LOG ---
None

--- EMAIL THREAD ---
Task Request: ${inputText}`;

    // Try primary model first, then fallback
    let completion;
    let modelUsed = this.primaryModel;

    try {
      // Build request configuration
      const requestConfig: any = {
        model: this.primaryModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: max_tokens,
        temperature: 0.65,
        top_p: 0.65,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };

      // Add reasoning configuration for models that support it
      const reasoningConfig = this.getReasoningConfig(this.primaryModel);
      if (reasoningConfig) {
        requestConfig.reasoning = reasoningConfig;
      }

      completion = await this.client!.chat.completions.create(requestConfig);
    } catch (primaryError) {
      // Try fallback model
      try {
        modelUsed = this.fallbackModel;
        
        const fallbackConfig: any = {
          model: this.fallbackModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: max_tokens,
          temperature: 0.65,
          top_p: 0.65,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        };

        const fallbackReasoningConfig = this.getReasoningConfig(this.fallbackModel);
        if (fallbackReasoningConfig) {
          fallbackConfig.reasoning = fallbackReasoningConfig;
        }

        completion = await this.client!.chat.completions.create(fallbackConfig);
      } catch (fallbackError) {
        throw primaryError; // Throw the original error
      }
    }

    try {
      const responseText = completion.choices[0]?.message?.content;

      if (!responseText) {
        throw new Error('No response received from AI service');
      }

      // Log the raw AI response for debugging
      console.log('=== RAW AI TURBO RESPONSE ===');
      console.log(responseText);
      console.log('=== END RAW RESPONSE ===');

      // Parse the combined response
      return this.parseTurboResponse(responseText, inputText, modelUsed, completion.usage?.total_tokens);

    } catch (error) {
      console.error('AI Turbo Service Error:', error);

      if (error instanceof OpenAI.APIError) {
        console.error('OpenAI API Error Details:', {
          status: error.status,
          message: error.message,
          type: error.type
        });

        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.status === 401) {
          throw new Error('API key authentication failed');
        }
        if (error.status === 400 && error.message.includes('content_filter')) {
          throw new Error('Content cannot be processed due to safety guidelines');
        }
      }

      // If it's a parsing error, preserve the original message for debugging
      if (error instanceof Error && (
        error.message.includes('Could not extract') ||
        error.message.includes('Invalid JSON') ||
        error.message.includes('Missing required fields')
      )) {
        throw error; // Re-throw parsing errors as-is for better debugging
      }

      throw new Error(`AI turbo processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse the combined turbo response into structured result
   */
  private parseTurboResponse(
    responseText: string,
    originalText: string,
    modelUsed: string,
    tokensUsed?: number
  ): TurboResult {
    console.log('=== PARSING TURBO RESPONSE ===');

    // Extract thinking content
    const thinkingMatch = responseText.match(/<thinking>([\s\S]*?)<\/thinking>/);
    const reasoningContent = thinkingMatch ? thinkingMatch[1].trim() : undefined;
    console.log('Thinking content extracted:', !!reasoningContent);

    // Try multiple patterns for enhanced text extraction
    let enhancedText = '';

    // Pattern 1: Look for **ENHANCED TEXT:** followed by **JSON_SEPARATOR**
    let enhancedMatch = responseText.match(/\*\*ENHANCED TEXT:\*\*\s*([\s\S]*?)\s*\*\*JSON_SEPARATOR\*\*/i);

    if (!enhancedMatch) {
      // Pattern 2: Look for content after thinking tags but before JSON_SEPARATOR
      const afterThinking = responseText.replace(/<thinking>[\s\S]*?<\/thinking>/g, '').trim();
      enhancedMatch = afterThinking.match(/([\s\S]*?)\s*\*\*JSON_SEPARATOR\*\*/i);
    }

    if (!enhancedMatch) {
      // Pattern 3: If no JSON_SEPARATOR, use everything after thinking tags
      const afterThinking = responseText.replace(/<thinking>[\s\S]*?<\/thinking>/g, '').trim();
      if (afterThinking) {
        enhancedText = afterThinking;
        console.log('Using fallback: content after thinking tags');
      } else {
        console.error('Could not extract enhanced text. Response structure:');
        console.error('Has thinking tags:', !!thinkingMatch);
        console.error('Has JSON_SEPARATOR:', responseText.includes('JSON_SEPARATOR'));
        console.error('Response length:', responseText.length);
        throw new Error('Could not extract enhanced text from response. Check console for details.');
      }
    } else {
      enhancedText = enhancedMatch[1].trim();
      console.log('Enhanced text extracted successfully');
    }

    // Extract JSON content
    const jsonMatch = responseText.match(/\*\*JSON_SEPARATOR\*\*\s*([\s\S]*?)$/i);
    if (!jsonMatch) {
      console.error('Could not find JSON_SEPARATOR in response');
      console.error('Response includes JSON_SEPARATOR:', responseText.includes('JSON_SEPARATOR'));

      // Fallback: try to find JSON-like content at the end
      const jsonPattern = /\{[\s\S]*\}$/;
      const fallbackJsonMatch = responseText.match(jsonPattern);

      if (!fallbackJsonMatch) {
        throw new Error('Could not extract JSON content from response. No JSON_SEPARATOR found.');
      }

      console.log('Using fallback JSON extraction');
      var jsonContent = fallbackJsonMatch[0];
    } else {
      var jsonContent = jsonMatch[1].trim();
      console.log('JSON content extracted successfully');
    }

    // Clean up markdown code blocks if present
    // Remove ```json and ``` wrappers
    jsonContent = jsonContent.replace(/^```json\s*/i, '').replace(/\s*```$/, '').trim();
    console.log('JSON content after markdown cleanup:', jsonContent.substring(0, 100) + '...');

    let parsedContent;
    try {
      parsedContent = JSON.parse(jsonContent);
      console.log('JSON parsed successfully');
    } catch (error) {
      console.error('JSON parsing failed. Raw JSON content:');
      console.error(jsonContent);
      throw new Error(`Invalid JSON in response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate required fields
    const missingFields = [];
    if (!parsedContent.title) missingFields.push('title');
    if (!parsedContent.description) missingFields.push('description');
    if (!Array.isArray(parsedContent.tasks)) missingFields.push('tasks (must be array)');

    if (missingFields.length > 0) {
      console.error('Missing required fields:', missingFields);
      console.error('Parsed content keys:', Object.keys(parsedContent));
      throw new Error(`Missing required fields in parsed content: ${missingFields.join(', ')}`);
    }

    console.log('=== PARSING SUCCESSFUL ===');

    return {
      enhanced_text: enhancedText,
      original_text: originalText,
      enhancement_applied: true,
      model_used: modelUsed,
      tokens_used: tokensUsed,
      reasoning_content: reasoningContent,
      title: parsedContent.title,
      event_log: parsedContent.eventLog || '',
      description: parsedContent.description,
      tasks: parsedContent.tasks || []
    };
  }
}
