/**
 * Field Mapping Utility for Task Master
 *
 * Handles conversion between API camelCase and database snake_case field names
 * to prevent field name mismatch bugs and ensure consistency across the system.
 *
 * This module provides:
 * - Bidirectional field mapping between API and database layers
 * - Validation of field name consistency
 * - Safe mapping with error handling
 * - Type-safe field transformations
 *
 * @example
 * ```typescript
 * // Convert API data to database format
 * const dbData = FieldMapper.apiToDb({
 *   fullDescription: 'Full description',
 *   dueDate: '2024-12-31T23:59:59Z'
 * });
 * // Result: { full_description: 'Full description', due_date: '2024-12-31T23:59:59Z' }
 *
 * // Convert database data to API format
 * const apiData = FieldMapper.dbToApi({
 *   full_description: 'Full description',
 *   due_date: '2024-12-31T23:59:59Z'
 * });
 * // Result: { fullDescription: 'Full description', dueDate: '2024-12-31T23:59:59Z' }
 * ```
 *
 * <AUTHOR> Master Development Team
 * @since 1.0.0
 */

// ValidationError import removed as it's not used in this file

/**
 * Mapping between API field names (camelCase) and database column names (snake_case)
 */
export const FIELD_MAPPINGS = {
  // Project fields
  fullDescription: 'full_description',
  eventLog: 'event_log',
  dueDate: 'due_date',
  completedDate: 'completed_date',
  createdBy: 'created_by',
  assignedTo: 'assigned_to',
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  createdByUsername: 'created_by_username',
  assignedToUsername: 'assigned_to_username',

  // Task fields
  projectId: 'project_id',
  parentTaskId: 'parent_task_id',

  // Comment fields
  parentCommentId: 'parent_comment_id',

  // File fields
  fileName: 'file_name',
  fileSize: 'file_size',
  mimeType: 'mime_type',
  uploadedBy: 'uploaded_by',
  uploadedAt: 'uploaded_at',

  // AI Enhancement fields
  inputText: 'input_text',
  enhancedText: 'enhanced_text',
  originalText: 'original_text',
  enhancementApplied: 'enhancement_applied',
  maxTokens: 'max_tokens',

  // Team member fields - CRITICAL MAPPINGS FOR BUG FIX
  userId: 'user_id',
  addedAt: 'added_at',
  addedBy: 'added_by',
  joinedAt: 'joined_at',
  teamMembers: 'team_members',

  // Activity log fields
  activityType: 'activity_type',
  entityType: 'entity_type',
  entityId: 'entity_id',
  oldValue: 'old_value',
  newValue: 'new_value',
  performedBy: 'performed_by',
  performedAt: 'performed_at',

  // Edit history fields - CRITICAL MAPPINGS FOR EDIT HISTORY BUG FIX
  fieldName: 'field_name',
  versionNumber: 'version_number',
  previousContent: 'previous_content',
  editedBy: 'edited_by',
  editedAt: 'edited_at',
  editedByUsername: 'edited_by_username',
  commentId: 'comment_id',
  isEdited: 'is_edited',
  editCount: 'edit_count',
  lastEditedAt: 'last_edited_at',
  lastEditedBy: 'last_edited_by',
  lastEditedByUsername: 'last_edited_by_username',

  // Additional common fields
  lastLogin: 'last_login',
  isActive: 'is_active',
  userEmail: 'user_email',
  fullName: 'full_name',
  displayName: 'display_name',
} as const;

/**
 * Reverse mapping from database column names to API field names
 */
export const REVERSE_FIELD_MAPPINGS = Object.fromEntries(
  Object.entries(FIELD_MAPPINGS).map(([api, db]) => [db, api])
) as Record<string, string>;

/**
 * Validation result interface
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: Record<string, string>;
}

/**
 * Field consistency report
 */
export interface ConsistencyReport {
  totalFields: number;
  consistentFields: number;
  inconsistentFields: string[];
  missingMappings: string[];
  unusedMappings: string[];
  recommendations: string[];
}

/**
 * Field Mapping Utility Class
 *
 * Provides static methods for converting field names between API and database formats.
 * Ensures consistent field naming across the application layers and prevents field
 * name mismatch bugs.
 *
 * @class FieldMapper
 * @static
 * @since 1.0.0
 */
export class FieldMapper {
  /**
   * Convert API field names (camelCase) to database column names (snake_case)
   *
   * Transforms an object with camelCase field names to snake_case field names
   * suitable for database operations. Uses the predefined FIELD_MAPPINGS for
   * known conversions and preserves unmapped fields as-is.
   *
   * @param {Record<string, any>} fields - Object with camelCase field names
   * @returns {Record<string, any>} Object with snake_case field names
   *
   * @example
   * ```typescript
   * const apiData = {
   *   fullDescription: 'Full description',
   *   dueDate: '2024-12-31T23:59:59Z',
   *   assignedTo: 123
   * };
   *
   * const dbData = FieldMapper.apiToDb(apiData);
   * // Result: {
   * //   full_description: 'Full description',
   * //   due_date: '2024-12-31T23:59:59Z',
   * //   assigned_to: 123
   * // }
   * ```
   *
   * @since 1.0.0
   */
  static apiToDb(fields: Record<string, any>, visited = new WeakSet()): Record<string, any> {
    const mapped: Record<string, any> = {};

    for (const [apiField, value] of Object.entries(fields)) {
      const dbField = FIELD_MAPPINGS[apiField as keyof typeof FIELD_MAPPINGS] || apiField;

      // Handle nested objects recursively with circular reference detection
      if (value && typeof value === 'object' && !Array.isArray(value) && value.constructor === Object) {
        if (visited.has(value)) {
          // Circular reference detected, skip to prevent infinite recursion
          mapped[dbField] = '[Circular Reference]';
        } else {
          visited.add(value);
          mapped[dbField] = this.apiToDb(value, visited);
          visited.delete(value);
        }
      } else {
        mapped[dbField] = value;
      }
    }

    return mapped;
  }

  /**
   * Convert database column names (snake_case) to API field names (camelCase)
   *
   * Transforms an object with snake_case field names to camelCase field names
   * suitable for API responses. Uses the predefined REVERSE_FIELD_MAPPINGS for
   * known conversions and preserves unmapped fields as-is.
   *
   * @param {Record<string, any>} fields - Object with snake_case field names
   * @returns {Record<string, any>} Object with camelCase field names
   *
   * @example
   * ```typescript
   * const dbData = {
   *   full_description: 'Full description',
   *   due_date: '2024-12-31T23:59:59Z',
   *   assigned_to: 123
   * };
   *
   * const apiData = FieldMapper.dbToApi(dbData);
   * // Result: {
   * //   fullDescription: 'Full description',
   * //   dueDate: '2024-12-31T23:59:59Z',
   * //   assignedTo: 123
   * // }
   * ```
   *
   * @since 1.0.0
   */
  static dbToApi(fields: Record<string, any>, visited = new WeakSet()): Record<string, any> {
    const mapped: Record<string, any> = {};

    for (const [dbField, value] of Object.entries(fields)) {
      const apiField = REVERSE_FIELD_MAPPINGS[dbField] || dbField;

      // Handle nested objects recursively with circular reference detection
      if (value && typeof value === 'object' && !Array.isArray(value) && value.constructor === Object) {
        if (visited.has(value)) {
          // Circular reference detected, skip to prevent infinite recursion
          mapped[apiField] = '[Circular Reference]';
        } else {
          visited.add(value);
          mapped[apiField] = this.dbToApi(value, visited);
          visited.delete(value);
        }
      } else {
        mapped[apiField] = value;
      }
    }

    return mapped;
  }

  /**
   * Validate field name consistency between API and database
   */
  static validateConsistency(apiFields: string[], dbFields: string[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: Record<string, string> = {};

    // Check for API fields that should be mapped but aren't
    for (const apiField of apiFields) {
      if (apiField in FIELD_MAPPINGS) {
        const expectedDbField = FIELD_MAPPINGS[apiField as keyof typeof FIELD_MAPPINGS];
        if (!dbFields.includes(expectedDbField)) {
          errors.push(`API field '${apiField}' maps to '${expectedDbField}' but database field not found`);
          suggestions[apiField] = expectedDbField;
        }
      } else if (this.shouldBeMapped(apiField)) {
        errors.push(`API field '${apiField}' appears to need mapping but no mapping defined`);
        suggestions[apiField] = this.suggestDbFieldName(apiField);
      }
    }

    // Check for database fields that should be mapped but aren't
    for (const dbField of dbFields) {
      if (dbField in REVERSE_FIELD_MAPPINGS) {
        const expectedApiField = REVERSE_FIELD_MAPPINGS[dbField];
        if (!apiFields.includes(expectedApiField)) {
          errors.push(`Database field '${dbField}' maps to '${expectedApiField}' but API field not found`);
          suggestions[dbField] = expectedApiField;
        }
      } else if (this.shouldBeMapped(dbField)) {
        errors.push(`Database field '${dbField}' appears to need mapping but no mapping defined`);
        suggestions[dbField] = this.suggestApiFieldName(dbField);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * Get the database field name for an API field
   */
  static getDbFieldName(apiField: string): string {
    return FIELD_MAPPINGS[apiField as keyof typeof FIELD_MAPPINGS] || apiField;
  }

  /**
   * Get the API field name for a database field
   */
  static getApiFieldName(dbField: string): string {
    return REVERSE_FIELD_MAPPINGS[dbField] || dbField;
  }

  /**
   * Check if a field name should be mapped (contains underscore or camelCase)
   */
  private static shouldBeMapped(fieldName: string): boolean {
    // Check for snake_case (contains underscore)
    if (fieldName.includes('_')) {
      return true;
    }
    
    // Check for camelCase (has uppercase letters after first character)
    if (/^[a-z][a-zA-Z]*[A-Z]/.test(fieldName)) {
      return true;
    }
    
    return false;
  }

  /**
   * Suggest database field name for API field (camelCase to snake_case)
   */
  private static suggestDbFieldName(apiField: string): string {
    return apiField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Suggest API field name for database field (snake_case to camelCase)
   */
  private static suggestApiFieldName(dbField: string): string {
    return dbField.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Validate that all required mappings exist for a set of fields
   */
  static validateMappings(fields: string[], direction: 'apiToDb' | 'dbToApi'): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: Record<string, string> = {};

    for (const field of fields) {
      if (direction === 'apiToDb') {
        if (this.shouldBeMapped(field) && !(field in FIELD_MAPPINGS)) {
          errors.push(`Missing mapping for API field: ${field}`);
          suggestions[field] = this.suggestDbFieldName(field);
        }
      } else {
        if (this.shouldBeMapped(field) && !(field in REVERSE_FIELD_MAPPINGS)) {
          errors.push(`Missing mapping for database field: ${field}`);
          suggestions[field] = this.suggestApiFieldName(field);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * Generate a consistency report for all defined mappings
   */
  static generateConsistencyReport(
    apiFields: string[],
    dbFields: string[]
  ): ConsistencyReport {
    const allMappedApiFields = Object.keys(FIELD_MAPPINGS);
    const allMappedDbFields = Object.values(FIELD_MAPPINGS);
    
    const consistentFields = allMappedApiFields.filter(apiField => {
      const dbField = FIELD_MAPPINGS[apiField as keyof typeof FIELD_MAPPINGS];
      return apiFields.includes(apiField) && dbFields.includes(dbField);
    });

    const inconsistentFields = allMappedApiFields.filter(apiField => {
      const dbField = FIELD_MAPPINGS[apiField as keyof typeof FIELD_MAPPINGS];
      return !apiFields.includes(apiField) || !dbFields.includes(dbField);
    });

    const missingMappings = [
      ...apiFields.filter(field => this.shouldBeMapped(field) && !(field in FIELD_MAPPINGS)),
      ...dbFields.filter(field => this.shouldBeMapped(field) && !(field in REVERSE_FIELD_MAPPINGS)),
    ];

    const unusedMappings = [
      ...allMappedApiFields.filter(field => !apiFields.includes(field)),
      ...allMappedDbFields.filter(field => !dbFields.includes(field)),
    ];

    const recommendations = [
      ...missingMappings.map(field => `Add mapping for field: ${field}`),
      ...unusedMappings.map(field => `Consider removing unused mapping: ${field}`),
    ];

    return {
      totalFields: allMappedApiFields.length,
      consistentFields: consistentFields.length,
      inconsistentFields,
      missingMappings,
      unusedMappings,
      recommendations,
    };
  }

  /**
   * Safely map fields with validation
   */
  static safeApiToDb(fields: Record<string, any>): { mapped: Record<string, any>; errors: string[] } {
    const mapped: Record<string, any> = {};
    const errors: string[] = [];

    for (const [apiField, value] of Object.entries(fields)) {
      try {
        const dbField = this.getDbFieldName(apiField);
        mapped[dbField] = value;
      } catch (error) {
        errors.push(`Failed to map API field '${apiField}': ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return { mapped, errors };
  }

  /**
   * Safely map fields with validation
   */
  static safeDbToApi(fields: Record<string, any>): { mapped: Record<string, any>; errors: string[] } {
    const mapped: Record<string, any> = {};
    const errors: string[] = [];

    for (const [dbField, value] of Object.entries(fields)) {
      try {
        const apiField = this.getApiFieldName(dbField);
        mapped[apiField] = value;
      } catch (error) {
        errors.push(`Failed to map database field '${dbField}': ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return { mapped, errors };
  }
}

/**
 * Utility function to create field mapping middleware
 */
export function createFieldMappingMiddleware(direction: 'apiToDb' | 'dbToApi') {
  return (fields: Record<string, any>) => {
    if (direction === 'apiToDb') {
      return FieldMapper.apiToDb(fields);
    } else {
      return FieldMapper.dbToApi(fields);
    }
  };
}

/**
 * Type-safe field mapping for specific entities
 */
export const EntityFieldMappers = {
  project: {
    apiToDb: (fields: Record<string, any>) => FieldMapper.apiToDb(fields),
    dbToApi: (fields: Record<string, any>) => FieldMapper.dbToApi(fields),
  },
  task: {
    apiToDb: (fields: Record<string, any>) => FieldMapper.apiToDb(fields),
    dbToApi: (fields: Record<string, any>) => FieldMapper.dbToApi(fields),
  },
  comment: {
    apiToDb: (fields: Record<string, any>) => FieldMapper.apiToDb(fields),
    dbToApi: (fields: Record<string, any>) => FieldMapper.dbToApi(fields),
  },
  file: {
    apiToDb: (fields: Record<string, any>) => FieldMapper.apiToDb(fields),
    dbToApi: (fields: Record<string, any>) => FieldMapper.dbToApi(fields),
  },
} as const;
