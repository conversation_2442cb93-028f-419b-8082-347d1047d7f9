/**
 * Task Master Types (LEGACY)
 *
 * ⚠️  DEPRECATION NOTICE ⚠️
 * This file contains legacy types with mixed naming conventions.
 *
 * NEW CODE SHOULD USE:
 * - @/lib/types/database for database operations (snake_case)
 * - @/lib/types/api for API operations (camelCase)
 * - @/lib/types/frontend for UI components (camelCase + UI state)
 *
 * This file is kept for backward compatibility but will be phased out.
 * Please migrate to the new layer-separated type system.
 */

/**
 * Common enum types
 */
export type TaskStatus = 'todo' | 'inProgress' | 'completed';
export type TaskPriority = 'low' | 'medium' | 'high';
export type TeamMemberRole = 'member' | 'admin' | 'viewer';
export type ActivityType =
  | 'upload'
  | 'comment'
  | 'comment_edit'
  | 'comment_delete'
  | 'status_change'
  | 'assignment'
  | 'due_date'
  | 'completion'
  | 'creation'
  | 'update'
  | 'task_creation'
  | 'task_completion'
  | 'project_details_edit'
  | 'event_log_edit';

/**
 * Base database entity interface
 * All database entities extend this for consistent structure
 */
export interface BaseEntity {
  id: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * User reference interface for joined queries
 */
export interface UserReference {
  id: number;
  username: string;
  email?: string;
}

/**
 * Project visibility type
 */
export type ProjectVisibility = 'public' | 'private';

/**
 * Project entity - main project structure
 */
export interface Project extends BaseEntity {
  title: string;
  description?: string;
  fullDescription?: string;
  eventLog?: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  dueDate?: string;
  completedDate?: string;
  visibility: ProjectVisibility;
  createdBy: number;
  assignedTo?: number;

  // Edit tracking fields for project fields
  fullDescriptionEditCount?: number;
  fullDescriptionLastEditedAt?: string;
  fullDescriptionLastEditedBy?: number;
  eventLogEditCount?: number;
  eventLogLastEditedAt?: string;
  eventLogLastEditedBy?: number;

  // Joined data from queries
  createdByUsername?: string;
  assignedToUsername?: string;
  fullDescriptionLastEditedByUsername?: string;
  eventLogLastEditedByUsername?: string;

  // Related entities (populated when needed)
  tags?: string[];
  teamMembers?: ProjectTeamMember[];
  files?: ProjectFile[];
  comments?: ProjectComment[];
  tasks?: Task[];
  activity?: ActivityLogEntry[];
}

/**
 * Task entity - hierarchical task structure
 */
export interface Task extends BaseEntity {
  projectId: number;
  parentTaskId?: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate?: string;
  completedDate?: string;
  assignedTo?: number;
  createdBy: number;
  
  // Joined data from queries
  assignedToUsername?: string;
  createdByUsername?: string;
  
  // Related entities
  subtasks?: Task[];
}

/**
 * Project team member entity
 */
export interface ProjectTeamMember extends BaseEntity {
  projectId: number;
  userId: number;
  role: TeamMemberRole;
  addedBy?: number;
  addedAt: string;

  // Joined user data
  username?: string;
  email?: string;
  name: string; // CRITICAL: Made required to prevent 'member.name is undefined' bug
  avatar?: string;
}

/**
 * Project file entity
 */
export interface ProjectFile extends BaseEntity {
  projectId: number;
  fileName: string;
  fileType?: string;
  fileSize?: number;
  filePath: string;
  thumbnailPath?: string;
  uploadedBy: number;
  uploadedAt: string;
  
  // Joined data
  uploadedByUsername?: string;
}

/**
 * Project comment entity
 */
export interface ProjectComment extends BaseEntity {
  projectId: number;
  parentCommentId?: number;
  authorId: number;
  content: string;

  // Edit tracking fields
  isEdited?: boolean;
  editCount?: number;
  lastEditedAt?: string;
  lastEditedBy?: number;

  // Joined data
  authorUsername?: string;
  authorEmail?: string;
  lastEditedByUsername?: string;

  // Related entities
  replies?: ProjectComment[];
  editHistory?: CommentEditHistory[];
}

/**
 * Comment edit history entity
 */
export interface CommentEditHistory extends BaseEntity {
  commentId: number;
  versionNumber: number;
  previousContent: string;
  editedBy: number;
  editedAt: string;

  // Joined data
  editedByUsername?: string;
}

/**
 * Project field edit history entity
 */
export interface ProjectEditHistory extends BaseEntity {
  projectId: number;
  fieldName: 'fullDescription' | 'eventLog';
  versionNumber: number;
  previousContent: string;
  editedBy: number;
  editedAt: string;

  // Joined data
  editedByUsername?: string;
}

/**
 * Activity log entry entity
 */
export interface ActivityLogEntry extends BaseEntity {
  projectId?: number;
  taskId?: number;
  userId: number;
  activityType: ActivityType;
  description: string;
  metadata?: string;
  
  // Joined data
  username?: string;
}

/**
 * Database-to-API mapping interfaces
 * These handle the snake_case to camelCase conversion
 */

/**
 * Raw database project structure (snake_case)
 */
export interface ProjectDbRow {
  id: number;
  title: string;
  description?: string;
  full_description?: string;
  event_log?: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  due_date?: string;
  completed_date?: string;
  visibility: ProjectVisibility;
  created_by: number;
  assigned_to?: number;
  full_description_edit_count?: number;
  full_description_last_edited_at?: string;
  full_description_last_edited_by?: number;
  event_log_edit_count?: number;
  event_log_last_edited_at?: string;
  event_log_last_edited_by?: number;
  created_at: string;
  updated_at: string;
  created_by_username?: string;
  assigned_to_username?: string;
  full_description_last_edited_by_username?: string;
  event_log_last_edited_by_username?: string;
}

/**
 * Raw database task structure (snake_case)
 */
export interface TaskDbRow {
  id: number;
  project_id: number;
  parent_task_id?: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  due_date?: string;
  completed_date?: string;
  assigned_to?: number;
  created_by: number;
  created_at: string;
  updated_at: string;
  assigned_to_username?: string;
  created_by_username?: string;
}

/**
 * Raw database team member structure (snake_case)
 */
export interface ProjectTeamMemberDbRow {
  id: number;
  project_id: number;
  user_id: number;
  role: TeamMemberRole;
  added_at: string;
  added_by?: number;
  created_at: string;
  updated_at: string;
  username?: string;
  email?: string;
}

/**
 * Raw database file structure (snake_case)
 */
export interface ProjectFileDbRow {
  id: number;
  project_id: number;
  file_name: string;
  file_type?: string;
  file_size?: number;
  file_path: string;
  thumbnail_path?: string;
  uploaded_by: number;
  uploaded_at: string;
  created_at: string;
  updated_at: string;
  uploaded_by_username?: string;
}

/**
 * Raw database comment structure (snake_case)
 */
export interface ProjectCommentDbRow {
  id: number;
  project_id: number;
  parent_comment_id?: number;
  author_id: number;
  content: string;
  is_edited?: boolean;
  edit_count?: number;
  last_edited_at?: string;
  last_edited_by?: number;
  created_at: string;
  updated_at: string;
  author_username?: string;
  author_email?: string;
  last_edited_by_username?: string;
}

/**
 * Raw database comment edit history structure (snake_case)
 */
export interface CommentEditHistoryDbRow {
  id: number;
  comment_id: number;
  version_number: number;
  previous_content: string;
  edited_by: number;
  edited_at: string;
  created_at: string;
  updated_at: string;
  edited_by_username?: string;
}

/**
 * Raw database project edit history structure (snake_case)
 */
export interface ProjectEditHistoryDbRow {
  id: number;
  project_id: number;
  field_name: 'full_description' | 'event_log';
  version_number: number;
  previous_content: string;
  edited_by: number;
  edited_at: string;
  created_at: string;
  updated_at: string;
  edited_by_username?: string;
}

/**
 * Raw database activity log structure (snake_case)
 */
export interface ActivityLogEntryDbRow {
  id: number;
  project_id?: number;
  task_id?: number;
  user_id: number;
  activity_type: ActivityType;
  description: string;
  metadata?: string;
  created_at: string;
  updated_at: string;
  username?: string;
}

/**
 * Data transformation utilities
 */
export class DataMapper {
  /**
   * Convert database project row to API project
   */
  static projectFromDb(row: ProjectDbRow): Project {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      fullDescription: row.full_description,
      eventLog: row.event_log,
      status: row.status,
      priority: row.priority,
      progress: row.progress,
      dueDate: row.due_date,
      completedDate: row.completed_date,
      visibility: row.visibility,
      createdBy: row.created_by,
      assignedTo: row.assigned_to,
      fullDescriptionEditCount: row.full_description_edit_count || undefined,
      fullDescriptionLastEditedAt: row.full_description_last_edited_at,
      fullDescriptionLastEditedBy: row.full_description_last_edited_by,
      eventLogEditCount: row.event_log_edit_count || undefined,
      eventLogLastEditedAt: row.event_log_last_edited_at,
      eventLogLastEditedBy: row.event_log_last_edited_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdByUsername: row.created_by_username,
      assignedToUsername: row.assigned_to_username,
      fullDescriptionLastEditedByUsername: row.full_description_last_edited_by_username,
      eventLogLastEditedByUsername: row.event_log_last_edited_by_username,
    };
  }

  /**
   * Convert database task row to API task
   */
  static taskFromDb(row: TaskDbRow): Task {
    return {
      id: row.id,
      projectId: row.project_id,
      parentTaskId: row.parent_task_id,
      title: row.title,
      description: row.description,
      status: row.status,
      priority: row.priority,
      dueDate: row.due_date,
      completedDate: row.completed_date,
      assignedTo: row.assigned_to,
      createdBy: row.created_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      assignedToUsername: row.assigned_to_username,
      createdByUsername: row.created_by_username,
    };
  }

  /**
   * Convert database team member row to API team member
   * CRITICAL FIX: Properly maps name field to prevent 'member.name is undefined' bug
   */
  static teamMemberFromDb(row: ProjectTeamMemberDbRow): ProjectTeamMember {
    // CRITICAL: Ensure name field is properly set from username or email
    const name = row.username || row.email || 'Unknown User';

    return {
      id: row.id,
      projectId: row.project_id,
      userId: row.user_id,
      role: row.role,
      addedAt: row.added_at,
      addedBy: row.added_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      username: row.username,
      email: row.email,
      name: name, // CRITICAL FIX: This was missing and caused the display bug
      avatar: row.avatar || undefined,
    };
  }

  /**
   * Convert database file row to API file
   */
  static fileFromDb(row: ProjectFileDbRow): ProjectFile {
    return {
      id: row.id,
      projectId: row.project_id,
      fileName: row.file_name,
      fileType: row.file_type,
      fileSize: row.file_size,
      filePath: row.file_path,
      thumbnailPath: row.thumbnail_path,
      uploadedBy: row.uploaded_by,
      uploadedAt: row.uploaded_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      uploadedByUsername: row.uploaded_by_username,
    };
  }

  /**
   * Convert database comment row to API comment
   */
  static commentFromDb(row: ProjectCommentDbRow): ProjectComment {
    return {
      id: row.id,
      projectId: row.project_id,
      parentCommentId: row.parent_comment_id,
      authorId: row.author_id,
      content: row.content,
      isEdited: row.is_edited || false,
      editCount: row.edit_count || undefined,
      lastEditedAt: row.last_edited_at,
      lastEditedBy: row.last_edited_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      authorUsername: row.author_username,
      authorEmail: row.author_email,
      lastEditedByUsername: row.last_edited_by_username,
    };
  }

  /**
   * Convert database comment edit history row to API comment edit history
   */
  static commentEditHistoryFromDb(row: CommentEditHistoryDbRow): CommentEditHistory {
    return {
      id: row.id,
      commentId: row.comment_id,
      versionNumber: row.version_number,
      previousContent: row.previous_content,
      editedBy: row.edited_by,
      editedAt: row.edited_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      editedByUsername: row.edited_by_username,
    };
  }

  /**
   * Convert database project edit history row to API project edit history
   */
  static projectEditHistoryFromDb(row: ProjectEditHistoryDbRow): ProjectEditHistory {
    // Convert field_name from snake_case to camelCase for API
    const fieldNameMapping: Record<string, 'fullDescription' | 'eventLog'> = {
      'full_description': 'fullDescription',
      'event_log': 'eventLog'
    };

    return {
      id: row.id,
      projectId: row.project_id,
      fieldName: fieldNameMapping[row.field_name] || row.field_name as any,
      versionNumber: row.version_number,
      previousContent: row.previous_content,
      editedBy: row.edited_by,
      editedAt: row.edited_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      editedByUsername: row.edited_by_username,
    };
  }

  /**
   * Convert database activity log row to API activity log
   */
  static activityFromDb(row: ActivityLogEntryDbRow): ActivityLogEntry {
    return {
      id: row.id,
      projectId: row.project_id,
      taskId: row.task_id,
      userId: row.user_id,
      activityType: row.activity_type,
      description: row.description,
      metadata: row.metadata,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      username: row.username,
    };
  }

  /**
   * Enhanced mapping methods with error handling and validation
   */

  /**
   * Safely map team member with comprehensive error handling
   * Prevents 'member.name is undefined' and other field mapping issues
   */
  static safeTeamMemberFromDb(row: unknown): ProjectTeamMember | null {
    try {
      if (!row || typeof row !== 'object') {
        console.warn('DataMapper.safeTeamMemberFromDb: Invalid row data', row);
        return null;
      }

      // Validate required fields
      if (!row.id || !row.user_id || !row.project_id) {
        console.warn('DataMapper.safeTeamMemberFromDb: Missing required fields', {
          id: row.id,
          user_id: row.user_id,
          project_id: row.project_id
        });
        return null;
      }

      return this.teamMemberFromDb(row as ProjectTeamMemberDbRow);
    } catch (error) {
      console.error('DataMapper.safeTeamMemberFromDb: Mapping error', error, row);
      return null;
    }
  }

  /**
   * Map array of team members with error handling
   * Filters out invalid entries to prevent display issues
   */
  static teamMembersFromDb(rows: unknown[]): ProjectTeamMember[] {
    if (!Array.isArray(rows)) {
      console.warn('DataMapper.teamMembersFromDb: Expected array, got', typeof rows);
      return [];
    }

    return rows
      .map(row => this.safeTeamMemberFromDb(row))
      .filter((member): member is ProjectTeamMember => member !== null);
  }

  /**
   * Bidirectional mapping validation
   * Ensures field mapping consistency between API and database formats
   */
  static validateMapping<T extends Record<string, unknown>>(
    original: T,
    mapped: T,
    direction: 'apiToDb' | 'dbToApi'
  ): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check for missing critical fields
    const criticalFields = direction === 'apiToDb'
      ? ['id', 'projectId', 'userId']
      : ['id', 'project_id', 'user_id'];

    for (const field of criticalFields) {
      if (original[field] !== undefined && mapped[field] === undefined) {
        errors.push(`Critical field '${field}' lost during ${direction} mapping`);
      }
    }

    // Check for type mismatches
    for (const [key, value] of Object.entries(original)) {
      if (mapped[key] !== undefined && typeof value !== typeof mapped[key]) {
        warnings.push(`Type mismatch for field '${key}': ${typeof value} -> ${typeof mapped[key]}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Generic safe mapping with error handling
   */
  static safeMap<TInput, TOutput>(
    data: TInput,
    mapperFn: (input: TInput) => TOutput,
    entityType: string
  ): TOutput | null {
    try {
      if (!data) {
        return null;
      }
      return mapperFn(data);
    } catch (error) {
      console.error(`DataMapper.safeMap: Error mapping ${entityType}`, error, data);
      return null;
    }
  }

  /**
   * Batch mapping with error handling
   */
  static safeBatchMap<TInput, TOutput>(
    items: TInput[],
    mapperFn: (input: TInput) => TOutput,
    entityType: string
  ): TOutput[] {
    if (!Array.isArray(items)) {
      console.warn(`DataMapper.safeBatchMap: Expected array for ${entityType}, got`, typeof items);
      return [];
    }

    return items
      .map(item => this.safeMap(item, mapperFn, entityType))
      .filter((mapped): mapped is TOutput => mapped !== null);
  }
}

/**
 * Input DTOs for API operations
 */

/**
 * Create project input
 */
export interface CreateProjectInput {
  title: string;
  description?: string;
  fullDescription?: string;
  eventLog?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  progress?: number;
  dueDate?: string;
  assignedTo?: number;
  visibility?: ProjectVisibility;
  tags?: string[];

  // Snake_case variants for API compatibility (after field mapping)
  full_description?: string;
  event_log?: string;
  due_date?: string;
  assigned_to?: number;
}

/**
 * Update project input
 */
export interface UpdateProjectInput {
  title?: string;
  description?: string;
  fullDescription?: string;
  eventLog?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  progress?: number;
  dueDate?: string;
  assignedTo?: number;
  completedDate?: string;
  visibility?: ProjectVisibility;

  // Snake_case variants for API compatibility
  full_description?: string;
  event_log?: string;
  due_date?: string;
  assigned_to?: number;
  completed_date?: string;
}

/**
 * Create task input
 */
export interface CreateTaskInput {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: string;
  assignedTo?: number;
  parentTaskId?: number;
}

/**
 * Update task input
 */
export interface UpdateTaskInput {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: string;
  assignedTo?: number;
  completedDate?: string;
}

/**
 * Create comment input
 */
export interface CreateCommentInput {
  content: string;
  parentCommentId?: number;
}

/**
 * Add team member input
 */
export interface AddTeamMemberInput {
  email: string;
  role?: TeamMemberRole;
}

/**
 * File upload input
 */
export interface FileUploadInput {
  file: File;
}

/**
 * Output DTOs for API responses
 */

/**
 * API response wrapper
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Project summary for list views
 */
export interface ProjectSummary {
  id: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  dueDate?: string;
  visibility: ProjectVisibility;
  createdBy: number;
  assignedTo?: number;
  createdAt: string;
  updatedAt: string;
  createdByUsername?: string;
  assignedToUsername?: string;
  tags: string[];
  taskCount: number;
  completedTaskCount: number;
  teamMemberCount: number;
}

/**
 * Task summary for list views
 */
export interface TaskSummary {
  id: number;
  projectId: number;
  parentTaskId?: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate?: string;
  assignedTo?: number;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  assignedToUsername?: string;
  createdByUsername?: string;
  subtaskCount: number;
  completedSubtaskCount: number;
}

/**
 * Legacy compatibility types
 * These maintain compatibility with existing frontend code
 */

/**
 * Legacy TaskMaster interface for frontend compatibility
 */
export interface TaskMaster {
  id: string; // Note: string for frontend compatibility
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  dueDate?: string;
  completedDate?: string;
  visibility: ProjectVisibility;
  assignee?: {
    name: string;
    avatar: string;
  };
  details?: string;
  createdBy: number;
  createdByUsername?: string;
  projectDetails?: {
    fullDescription: string;
    tags: string[];
    teamMembers: TeamMember[];
    files: ProjectFile[];
    comments: ProjectComment[];
    eventLog: ActivityLogEntry[];
  };
  tasks?: Task[];
}

/**
 * Legacy TeamMember interface for frontend compatibility
 */
export interface TeamMember {
  id: number;
  projectId: number;
  userId: number;
  role: string;
  addedAt: string;
  addedBy?: number;
  username?: string;
  email?: string;
  name: string;
  avatar?: string;
}

/**
 * Legacy TaskMasterColumn interface for frontend compatibility
 */
export interface TaskMasterColumn {
  id: TaskStatus;
  title: string;
  icon: string;
  variant: 'neutral' | 'warning' | 'success' | 'brand';
  count: number;
  tasks: TaskMaster[];
}

/**
 * Legacy ProjectFlowBoardProps interface for frontend compatibility
 */
export interface ProjectFlowBoardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: TaskMaster | null;
  onProjectDetailsModalOpen?: (description: string) => void;
  projectDescription?: string;
  onProjectDescriptionChange?: (description: string) => void;
  onCreateTask?: (projectId: string, taskData: {
    title: string;
    description?: string;
    parentTaskId?: number;
  }) => Promise<void>;
  onUpdateTask?: (taskId: number, updateData: {
    status?: TaskStatus;
    title?: string;
    description?: string;
  }) => Promise<void>;
  onDeleteTask?: (taskId: number) => Promise<void>;
  onFileUploaded?: () => void;
  onFileDeleted?: (fileId: number) => void;
  onTeamUpdated?: () => void;
  onCommentAdded?: () => void;
  currentUserId?: string;
}
